package ru.sbertroika.passenger.tkp3.transaction.loader

import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.clickhouse.ClickHouseContainer
import org.testcontainers.containers.KafkaContainer
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import org.testcontainers.utility.DockerImageName
import java.time.Duration

@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
open class BaseIntegrationTest {

    companion object {
        @Container
        val kafkaContainer = KafkaContainer(DockerImageName.parse("confluentinc/cp-kafka:7.4.0"))
            .withExposedPorts(9092, 9093)
            .withStartupTimeout(Duration.ofMinutes(1))

        @Container
        val postgresContainer = PostgreSQLContainer(DockerImageName.parse("postgres:15"))
            .withDatabaseName("tkp3_transaction_loader")
            .withUsername("postgres")
            .withPassword("postgres")
            .withInitScript("postgres-init.sql")
            .withStartupTimeout(Duration.ofMinutes(1))

        @Container
        val clickhouseContainer = ClickHouseContainer(DockerImageName.parse("clickhouse/clickhouse-server:latest"))
            .withDatabaseName("tkp3_transaction_loader")
            .withUsername("admin")
            .withPassword("admin")
            .withCopyFileToContainer(
                org.testcontainers.utility.MountableFile.forClasspathResource("clickhouse-init.sql"),
                "/docker-entrypoint-initdb.d/clickhouse-init.sql"
            )
            .withStartupTimeout(Duration.ofMinutes(1))

        @DynamicPropertySource
        @JvmStatic
        fun registerDynamicProperties(registry: DynamicPropertyRegistry) {
            // Kafka properties
            registry.add("spring.kafka.bootstrap-servers") { kafkaContainer.bootstrapServers }
            
            // PostgreSQL properties
            registry.add("spring.r2dbc.url") {
                "r2dbc:postgresql://${postgresContainer.username}:${postgresContainer.password}@${postgresContainer.host}:${postgresContainer.getMappedPort(5432)}/${postgresContainer.databaseName}"
            }
            
            // ClickHouse properties
            registry.add("spring.clickhouse.url") {
                "r2dbc:clickhouse://${clickhouseContainer.username}:${clickhouseContainer.password}@${clickhouseContainer.host}:${
                    clickhouseContainer.getMappedPort(8123)
                }/${clickhouseContainer.databaseName}"
            }
        }
    }
}
