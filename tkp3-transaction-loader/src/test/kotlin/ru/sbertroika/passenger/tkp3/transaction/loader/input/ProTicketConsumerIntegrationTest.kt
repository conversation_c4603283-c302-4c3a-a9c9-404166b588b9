package ru.sbertroika.passenger.tkp3.transaction.loader.input

import kotlinx.coroutines.runBlocking
import org.jeasy.random.EasyRandom
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.core.ConsumerFactory
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.test.context.EmbeddedKafka
import org.springframework.kafka.test.utils.KafkaTestUtils
import ru.sbertroika.passenger.tkp3.transaction.loader.BaseIntegrationTest
import ru.sbertroika.passenger.tkp3.transaction.loader.output.clickhouse.EmvTrxClickHouseService
import ru.sbertroika.passenger.tkp3.transaction.loader.output.model.PassengerTransactionModel
import ru.sbertroika.passenger.tkp3.transaction.loader.output.repository.RouteCrudRepository
import ru.sbertroika.passenger.tkp3.transaction.loader.output.repository.VehicleRepository
import ru.sbertroika.passenger.tkp3.transaction.loader.util.mapper
import ru.sbertroika.tkp3.pro.model.PayMethodType
import ru.sbertroika.tkp3.pro.model.Ticket
import ru.sbertroika.tkp3.pro.model.Vehicle
import ru.sbertroika.tkp3.pro.model.VehicleType
import ru.sbertroika.tkp3.pro.model.VehicleStatus
import ru.sbertroika.tkp3.pro.model.Route
import ru.sbertroika.tkp3.pro.model.RouteStatus
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*

@EmbeddedKafka(
    topics = ["test.pro.internal.ticket", "test.passenger.transaction.in"],
    partitions = 1
)
class ProTicketConsumerIntegrationTest : BaseIntegrationTest() {

    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, String>

    @Autowired
    private lateinit var consumerFactory: ConsumerFactory<String, String>

    @Autowired
    private lateinit var emvTrxClickHouseService: EmvTrxClickHouseService

    @Autowired
    private lateinit var routeRepository: RouteCrudRepository

    @Autowired
    private lateinit var vehicleRepository: VehicleRepository

    private val mapper = mapper()
    private val ez = EasyRandom()

    @Test
    fun `should process ticket and send passenger transaction`() = runBlocking {
        // Given - создаем даты в начале теста
        val createdAt = ZonedDateTime.now(ZoneOffset.UTC)
        val recordAt = LocalDateTime.now()

        val trxId = UUID.randomUUID()
        val routeId = UUID.randomUUID()
        val projectId = UUID.fromString("550e8400-e29b-41d4-a716-************") // from regions.json -> region 77
        val ticketId = UUID.randomUUID()
        val carrierId = UUID.randomUUID()
        val vehicleId = UUID.randomUUID()
        val routeVersion = 1

        // Create EmvTrx in ClickHouse
        val emvTrx = ez.nextObject(ru.sbertroika.passenger.tkp3.transaction.loader.output.clickhouse.model.EmvTrx::class.java).copy(
            trxId = trxId,
            createdAt = createdAt.toLocalDateTime(),
            recordAt = recordAt,
            crdId = carrierId
        )
        emvTrxClickHouseService.insert(emvTrx)

        // Create Route in PostgreSQL using EasyRandom
        val route = ez.nextObject(Route::class.java).copy(
            id = routeId,
            version = routeVersion,
            projectId = projectId,
            status = RouteStatus.ACTIVE
        )
        routeRepository.save(route)

        // Create Vehicle in PostgreSQL using EasyRandom
        val vehicle = ez.nextObject(Vehicle::class.java).copy(
            id = vehicleId,
            version = routeVersion,
            projectId = projectId,
            type = VehicleType.BUS,
            number = "BUS123",
            status = VehicleStatus.ACTIVE
        )
        vehicleRepository.save(vehicle)

        // Create Ticket using EasyRandom
        val ticket = ez.nextObject(Ticket::class.java).copy(
            ticketId = ticketId,
            projectId = projectId,
            createdAt = createdAt,
            payMethodType = PayMethodType.EMV,
            routeId = routeId,
            routeVersion = 1,
            trxId = trxId,
            vehicleId = vehicleId
        )

        // When
        val ticketJson = mapper.writeValueAsString(ticket)
        kafkaTemplate.send("test.pro.internal.ticket", "test-key", ticketJson)
        kafkaTemplate.flush()

        // Then - wait and consume message from output topic
        val consumer = consumerFactory.createConsumer("test-group", "test-consumer")
        consumer.subscribe(listOf("test.passenger.transaction.in"))
        
        val consumerRecord = KafkaTestUtils.getSingleRecord(
            consumer,
            "test.passenger.transaction.in"
        )

        val passengerTransaction = mapper.readValue(consumerRecord.value(), PassengerTransactionModel::class.java)

        // Assertions
        assertNotNull(passengerTransaction)
        assertEquals(trxId.toString(), passengerTransaction.id)
        assertEquals("77", passengerTransaction.region) // from regions.json
        assertEquals(carrierId.toString(), passengerTransaction.carrierId)
        assertEquals("EMV", passengerTransaction.carrierType)
        assertEquals(createdAt.format(DateTimeFormatter.ISO_INSTANT), passengerTransaction.createdAt)
        assertEquals(ticket.terminalSerial, passengerTransaction.terminalSerialNumber)
        assertEquals(ticket.ticketSeries, passengerTransaction.ticketSeries)
        assertEquals(ticket.ticketNumber, passengerTransaction.ticketNumber)
        assertEquals(ticket.amount?.toLong(), passengerTransaction.amount)
        assertEquals(0, passengerTransaction.status) // EMV = 0
        assertEquals(vehicle.type?.ordinal, passengerTransaction.transportTypeId)
        assertEquals(route.number, passengerTransaction.routeNumber)
        assertEquals(route.name, passengerTransaction.routeName)
        assertEquals(createdAt.format(DateTimeFormatter.ISO_INSTANT), passengerTransaction.rideDateTime)
        assertEquals(projectId, passengerTransaction.projectId)

        assertEquals(trxId.toString(), consumerRecord.key())
    }
}
