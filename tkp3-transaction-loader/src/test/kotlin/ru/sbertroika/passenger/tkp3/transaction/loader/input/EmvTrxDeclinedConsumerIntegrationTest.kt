package ru.sbertroika.passenger.tkp3.transaction.loader.input

import kotlinx.coroutines.runBlocking
import org.jeasy.random.EasyRandom
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.core.ConsumerFactory
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.test.context.EmbeddedKafka
import org.springframework.kafka.test.utils.KafkaTestUtils
import ru.sbertroika.passenger.tkp3.transaction.loader.BaseIntegrationTest
import ru.sbertroika.passenger.tkp3.transaction.loader.input.model.EmvTrxDeclined
import ru.sbertroika.passenger.tkp3.transaction.loader.output.clickhouse.TicketClickHouseService
import ru.sbertroika.passenger.tkp3.transaction.loader.output.model.TransactionStatusModel
import ru.sbertroika.passenger.tkp3.transaction.loader.util.mapper
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*

@EmbeddedKafka(
    topics = ["test.emv.trx.declined", "test.passenger.transaction.status.in"],
    partitions = 1
)
class EmvTrxDeclinedConsumerIntegrationTest : BaseIntegrationTest() {

    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, String>

    @Autowired
    private lateinit var consumerFactory: ConsumerFactory<String, String>
    @Autowired
    private lateinit var ticketClickHouseService: TicketClickHouseService

    private val mapper = mapper()
    private val ez = EasyRandom()

    @Test
    fun `should process emv trx declined and send transaction status`() = runBlocking {
        // Given - создаем даты в начале теста
        val createdAt = ZonedDateTime.now(ZoneOffset.UTC)
        val recordAt = LocalDateTime.now()

        val trxId = UUID.randomUUID()
        val projectId = UUID.fromString("550e8400-e29b-41d4-a716-************") // from regions.json -> region 77
        val ticketId = UUID.randomUUID()

        // Create Ticket in ClickHouse
        val ticket = ez.nextObject(ru.sbertroika.passenger.tkp3.transaction.loader.output.clickhouse.model.Ticket::class.java).copy(
            trxId = trxId,
            projectId = projectId,
            ticketId = ticketId,
            createdAt = createdAt.toLocalDateTime(),
            recordAt = recordAt,
            payMethodType = "EMV"
        )
        ticketClickHouseService.insert(ticket)

        // Create EmvTrxDeclined using EasyRandom
        val emvTrxDeclined = ez.nextObject(EmvTrxDeclined::class.java).copy(
            trxId = trxId,
            createdAt = createdAt,
            recordAt = createdAt
        )

        // When
        val emvTrxDeclinedJson = mapper.writeValueAsString(emvTrxDeclined)
        kafkaTemplate.send("test.emv.trx.declined", "test-key", emvTrxDeclinedJson)
        kafkaTemplate.flush()

        // Then - wait and consume message from output topic
        val consumer = consumerFactory.createConsumer("test-group", "test-consumer")
        consumer.subscribe(listOf("test.passenger.transaction.status.in"))
        
        val consumerRecord = KafkaTestUtils.getSingleRecord(
            consumer,
            "test.passenger.transaction.status.in"
        )

        val transactionStatus = mapper.readValue(consumerRecord.value(), TransactionStatusModel::class.java)

        // Assertions
        assertNotNull(transactionStatus)
        assertEquals(trxId.toString(), transactionStatus.id)
        assertEquals("77", transactionStatus.region) // from regions.json
        assertEquals(2, transactionStatus.status) // FAILED = 2
        assertEquals(createdAt.format(DateTimeFormatter.ISO_INSTANT), transactionStatus.createdAt)
        assertEquals(projectId.toString(), transactionStatus.projectId)

        assertEquals(trxId.toString(), consumerRecord.key())
    }
}
