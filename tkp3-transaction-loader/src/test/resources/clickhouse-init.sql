CREATE DATABASE IF NOT EXISTS tkp3_transaction_loader;

USE tkp3_transaction_loader;

CREATE TABLE emv_trx
(
    `trx_id` UUID COMMENT 'Идентификатор транзакции (заказа)',
    `created_at` DateTime('UTC') COMMENT 'Время формирования транзакции на терминале',
    `record_at` DateTime('UTC') COMMENT 'Время формирования транзакции на сервере',
    `crd_id` UUID COMMENT 'Идентификатор карты',
    `terminal_serial` String COMMENT 'Заводской номер терминала',
    `tid` String COMMENT 'Идентификатор терминала',
    `shift_num` UInt16 COMMENT 'Номер смены на терминале',
    `ern` UInt16 COMMENT 'Единый номер операции на терминале (уникальный в рамках смены)',
    `tap_in` Bool DEFAULT false COMMENT 'Check-in',
    `tap_out` Bool DEFAULT false COMMENT 'Check-out',
    `amount` UInt16 COMMENT 'Сумма авторизации',
    `tags` Nullable(String) COMMENT 'Тэги'
)
ENGINE = MergeTree
PARTITION BY toYYYYMM(created_at)
PRIMARY KEY trx_id
ORDER BY trx_id
SETTINGS index_granularity = 8192;

CREATE TABLE ticket
(
    `project_id` UUID COMMENT 'Идентификатор проекта',
    `org_id` UUID COMMENT 'Идентификатор организации',
    `ticket_id` UUID COMMENT 'Идентификатор билета',
    `created_at` DateTime('UTC') COMMENT 'Время формирования билета на терминале',
    `record_at` DateTime('UTC') COMMENT 'Время формирования билета на сервере',
    `ticket_series` String COMMENT 'Серия билета',
    `ticket_number` String COMMENT 'Номер билета',
    `pr_id` UUID COMMENT 'Идентификатор продукта',
    `pr_ver` UInt16 COMMENT 'Версия записи о продукте',
    `t_id` UUID COMMENT 'Идентификатор тарифа',
    `t_ver` UInt16 COMMENT 'Версия записи о тарифе',
    `r_id` UUID COMMENT 'Идентификатор маршрута',
    `r_ver` UInt16 COMMENT 'Версия записи о маршруте',
    `v_id` UUID COMMENT 'Идентификатор Т/С',
    `v_ver` UInt16 COMMENT 'Версия записи о Т/С',
    `driver_id` Nullable(UUID) COMMENT 'Идентификатор водителя',
    `conductor_id` Nullable(UUID) COMMENT 'Идентификатор кондуктора',
    `st_from_id` Nullable(UUID) COMMENT 'Станция входа',
    `st_from_ver` Nullable(UInt16) COMMENT 'Версия записи о станции входа',
    `st_to_id` Nullable(UUID) COMMENT 'Станция выхода',
    `st_to_ver` Nullable(UInt16) COMMENT 'Версия записи о станции выхода',
    `a_id` Nullable(UUID) COMMENT 'Идентификатор абонемента',
    `a_ver` Nullable(UInt16) COMMENT 'Версия записи об абонементе',
    `wa_id` Nullable(UUID) COMMENT 'Идентификатор кошелька',
    `wa_ver` Nullable(UInt16) COMMENT 'Версия записи о кошельке',
    `trx_id` UUID COMMENT 'Идентификатор транзакции (заказа)',
    `amount` UInt16 COMMENT 'Cтоимость билета',
    `terminal_serial` String COMMENT 'Заводской номер терминала',
    `tid` Nullable(String) COMMENT 'Идентификатор терминала',
    `shift_num` UInt16 COMMENT 'Номер смены на терминале',
    `ern` UInt16 COMMENT 'Единый номер операции на терминале (в рамках смены)',
    `tags` Nullable(String) COMMENT 'Тэги',
    `terminal_id` UUID COMMENT 'Идентификатор терминала',
    `cashier_id` Nullable(UUID) COMMENT 'Идентификатор кассира',
    `pay_method_type` Enum8('CASH' = 0,
 'EMV' = 1,
 'TROIKA_TICKET' = 2,
 'TROIKA_WALLET' = 3,
 'ABT_TICKET' = 4,
 'ABT_WALLET' = 5) COMMENT 'Тип способа оплаты'
)
ENGINE = MergeTree
PARTITION BY (project_id,
 org_id,
 toYYYYMM(created_at))
PRIMARY KEY (ticket_series,
 ticket_number)
ORDER BY (ticket_series,
 ticket_number)
SETTINGS index_granularity = 8192;
