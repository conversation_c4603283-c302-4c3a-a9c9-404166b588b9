spring:
  main:
    allow-bean-definition-overriding: true

  r2dbc:
    url: r2dbc:postgresql://postgres:postgres@localhost:5432/tkp3_transaction_loader

  clickhouse:
    url: r2dbc:clickhouse://admin:admin@localhost:8123/tkp3_transaction_loader

  kafka:
    bootstrap-servers: localhost:9092
    pro_internal_ticket_topic: test.pro.internal.ticket
    passenger_transaction_in_topic: test.passenger.transaction.in
    passenger_transaction_status_in_topic: test.passenger.transaction.status.in
    emv_trx_auth_topic: test.emv.trx.auth
    emv_trx_declined_topic: test.emv.trx.declined
    tkp3_transaction_loader_group: test_group

regions:
  file:
    path: classpath:regions.json
