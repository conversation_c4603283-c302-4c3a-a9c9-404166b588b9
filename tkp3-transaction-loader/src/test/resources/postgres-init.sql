-- Create route_scheme_type enum if not exists
DO $$ BEGIN
    CREATE TYPE route_scheme_type AS ENUM ('DIRECTIONAL', 'CIRCLE');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create route_type enum if not exists
DO $$ BEGIN
    CREATE TYPE route_type AS ENUM ('URBAN', 'SUBURBAN');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create route_status enum if not exists
DO $$ BEGIN
    CREATE TYPE route_status AS ENUM ('ACTIVE', 'DISABLED', 'BLOCKED', 'IS_DELETED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create route table
CREATE TABLE IF NOT EXISTS route (
    r_id UUID DEFAULT gen_random_uuid() NOT NULL,
    r_version INTEGER DEFAULT 1 NOT NULL,
    PRIMARY KEY (r_id, r_version),
    r_version_created_at TIMESTAMP DEFAULT now() NOT NULL,
    r_version_created_by UUID,
    r_active_from TIMESTAMP DEFAULT now() NOT NULL,
    r_active_till TIMESTAMP,
    r_project_id UUID NOT NULL,
    r_index INTEGER,
    r_name VARCHAR(500),
    r_number VARCHAR(40) NOT NULL DEFAULT '0',
    r_scheme route_scheme_type NOT NULL DEFAULT 'DIRECTIONAL',
    r_type route_type NOT NULL DEFAULT 'URBAN',
    r_status route_status,
    tags TEXT DEFAULT '' NOT NULL
);

-- Create index on route table
CREATE INDEX IF NOT EXISTS idx_route_id_version ON route(r_id, r_version);

-- Create vehicle_type enum if not exists
DO $$ BEGIN
    CREATE TYPE vehicle_type AS ENUM ('BUS', 'TRAM', 'TROLLEYBUS', 'METRO');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create vehicle_status enum if not exists
DO $$ BEGIN
    CREATE TYPE vehicle_status AS ENUM ('ACTIVE', 'DISABLED', 'BLOCKED', 'IS_DELETED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create vehicle table
CREATE TABLE IF NOT EXISTS vehicle (
    vh_id UUID DEFAULT gen_random_uuid() NOT NULL,
    vh_version INTEGER DEFAULT 1 NOT NULL,
    PRIMARY KEY (vh_id, vh_version),
    vh_version_created_at TIMESTAMP DEFAULT now() NOT NULL,
    vh_version_created_by UUID,
    v_project_id UUID NOT NULL,
    v_organization_id UUID NOT NULL,
    vh_type vehicle_type,
    vh_number VARCHAR(150),
    vh_status vehicle_status,
    tags TEXT DEFAULT '' NOT NULL
);
