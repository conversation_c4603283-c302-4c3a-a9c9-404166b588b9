package ru.sbertroika.passenger.tkp3.transaction.loader.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.tkp3.pro.model.Route
import ru.sbertroika.tkp3.pro.model.RoutePK
import java.util.*

@Repository
interface RouteCrudRepository : CoroutineCrudRepository<Route, RoutePK> {

    @Query("""
        SELECT * FROM route 
        WHERE r_id = :routeId 
        AND r_version = :routeVersion
        AND r_status != 'IS_DELETED'
    """)
    suspend fun findByRouteIdAndVersion(routeId: UUID, routeVersion: Int): Route?
}
