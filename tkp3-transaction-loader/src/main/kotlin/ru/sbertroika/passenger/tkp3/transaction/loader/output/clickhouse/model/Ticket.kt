package ru.sbertroika.passenger.tkp3.transaction.loader.output.clickhouse.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime
import java.util.*

@Table("ticket")
data class Ticket(
    @Column("project_id")
    val projectId: UUID,
    
    @Column("org_id")
    val orgId: UUID,
    
    @Column("ticket_id")
    val ticketId: UUID,
    
    @Column("created_at")
    val createdAt: LocalDateTime,
    
    @Column("record_at")
    val recordAt: LocalDateTime,
    
    @Column("ticket_series")
    val ticketSeries: String,
    
    @Column("ticket_number")
    val ticketNumber: String,
    
    @Column("pr_id")
    val prId: UUID,
    
    @Column("pr_ver")
    val prVer: Int,
    
    @Column("t_id")
    val tId: UUID,
    
    @Column("t_ver")
    val tVer: Int,
    
    @Column("r_id")
    val rId: UUID,
    
    @Column("r_ver")
    val rVer: Int,
    
    @Column("v_id")
    val vId: UUID,
    
    @Column("v_ver")
    val vVer: Int,
    
    @Column("driver_id")
    val driverId: UUID?,
    
    @Column("conductor_id")
    val conductorId: UUID?,
    
    @Column("st_from_id")
    val stFromId: UUID?,
    
    @Column("st_from_ver")
    val stFromVer: Int?,
    
    @Column("st_to_id")
    val stToId: UUID?,
    
    @Column("st_to_ver")
    val stToVer: Int?,
    
    @Column("a_id")
    val aId: UUID?,
    
    @Column("a_ver")
    val aVer: Int?,
    
    @Column("wa_id")
    val waId: UUID?,
    
    @Column("wa_ver")
    val waVer: Int?,
    
    @Column("trx_id")
    val trxId: UUID,
    
    @Column("amount")
    val amount: Int,
    
    @Column("terminal_serial")
    val terminalSerial: String,
    
    @Column("tid")
    val tid: String?,
    
    @Column("shift_num")
    val shiftNum: Int,
    
    @Column("ern")
    val ern: Int,
    
    @Column("tags")
    val tags: String?,
    
    @Column("terminal_id")
    val terminalId: UUID,
    
    @Column("cashier_id")
    val cashierId: UUID?,
    
    @Column("pay_method_type")
    val payMethodType: String
)
