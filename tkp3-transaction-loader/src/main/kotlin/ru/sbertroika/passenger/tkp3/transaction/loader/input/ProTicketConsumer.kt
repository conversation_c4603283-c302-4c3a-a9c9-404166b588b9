package ru.sbertroika.passenger.tkp3.transaction.loader.input

import com.fasterxml.jackson.module.kotlin.readValue
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.slf4j.LoggerFactory
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component
import ru.sbertroika.passenger.tkp3.transaction.loader.output.service.TransactionService
import ru.sbertroika.passenger.tkp3.transaction.loader.output.exceptions.EmvTrxNotReady
import ru.sbertroika.passenger.tkp3.transaction.loader.output.exceptions.RegionNotFoundInMap
import ru.sbertroika.passenger.tkp3.transaction.loader.util.mapper
import ru.sbertroika.tkp3.pro.model.Ticket
import java.time.Duration

@Component
class ProTicketConsumer(
    private val transactionService: TransactionService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val mapper = mapper()

    @KafkaListener(
        groupId = "\${spring.kafka.tkp3_transaction_loader_group}",
        topics = ["\${spring.kafka.pro_internal_ticket_topic}"]
    )
    fun receive(record: ConsumerRecord<String, String>, acknowledgment: Acknowledgment) = runBlocking {
        val ticket = mapper.readValue<Ticket>(record.value())

        transactionService.sendTransaction(ticket)
            .fold(
                { error ->
                    when (error) {
                        is EmvTrxNotReady -> {
                            // EmvTrx не готов - откладываем на 1 минуту
                            logger.info("EmvTrx not ready for ticket [${ticket}], will retry in 1 minute", error)
                            acknowledgment.nack(Duration.ofMinutes(1))
                        }
                        is RegionNotFoundInMap -> {
                            // Регион не найден - логируем ошибку и помечаем как прочитанное
                            logger.error("Region not found for ticket [${ticket}], will retry in 2 minute", error)
                            acknowledgment.nack(Duration.ofMinutes(2))
                        }
                        else -> {
                            // Другие ошибки - логируем и помечаем как прочитанное
                            logger.error("Error processing ticket [${ticket}], will retry in 2 minute", error)
                            acknowledgment.nack(Duration.ofMinutes(2))
                        }
                    }
                },
                {
                    // Успешная обработка
                    acknowledgment.acknowledge()
                }
            )
    }
}