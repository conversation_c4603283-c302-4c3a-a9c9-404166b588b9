package ru.sbertroika.passenger.tkp3.transaction.loader.output.clickhouse

import arrow.core.Either
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.domain.Sort
import org.springframework.data.r2dbc.core.R2dbcEntityOperations
import org.springframework.data.relational.core.query.Criteria
import org.springframework.data.relational.core.query.Query
import org.springframework.stereotype.Service
import ru.sbertroika.passenger.tkp3.transaction.loader.output.clickhouse.model.Ticket
import java.time.format.DateTimeFormatter
import java.util.*

@Service
class TicketClickHouseService(
    @Qualifier("clickhouseR2dbcEntityOperations")
    private val entityTemplate: R2dbcEntityOperations
) {

    suspend fun findByTrxId(trxId: UUID): Either<Throwable, Ticket?> = Either.catch {
        val search = mutableListOf<Criteria>()
        search.add(Criteria.where("trx_id").`is`(trxId))

        withContext(Dispatchers.IO) {
            entityTemplate.select(Ticket::class.java)
                .matching(
                    Query.query(Criteria.from(search))
                        .sort(Sort.by("createdAt").descending())
                        .limit(1)
                ).all()
                .collectList()
                .block()
                ?.firstOrNull()
        }
    }

    private fun org.springframework.r2dbc.core.DatabaseClient.GenericExecuteSpec.bindNullable(
        name: String, 
        value: Any?
    ): org.springframework.r2dbc.core.DatabaseClient.GenericExecuteSpec {
        return if (value != null) {
            this.bind(name, value)
        } else {
            this.bindNull(name, Any::class.java)
        }
    }

    suspend fun insert(ticket: Ticket): Either<Throwable, Unit> = Either.catch {
        withContext(Dispatchers.IO) {
            val sql = """
                INSERT INTO ticket (
                    project_id, org_id, ticket_id, created_at, record_at, ticket_series, ticket_number,
                    pr_id, pr_ver, t_id, t_ver, r_id, r_ver, v_id, v_ver, driver_id, conductor_id,
                    st_from_id, st_from_ver, st_to_id, st_to_ver, a_id, a_ver, wa_id, wa_ver,
                    trx_id, amount, terminal_serial, tid, shift_num, ern, tags, terminal_id, cashier_id, pay_method_type
                ) VALUES (
                    '${ticket.projectId}', 
                    '${ticket.orgId}', 
                    '${ticket.ticketId}', 
                    '${ticket.createdAt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))}', 
                    '${ticket.recordAt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))}', 
                    '${ticket.ticketSeries}', 
                    '${ticket.ticketNumber}', 
                    '${ticket.prId}', 
                    ${ticket.prVer}, 
                    '${ticket.tId}', 
                    ${ticket.tVer}, 
                    '${ticket.rId}', 
                    ${ticket.rVer}, 
                    '${ticket.vId}', 
                    ${ticket.vVer}, 
                    ${if (ticket.driverId != null) "'${ticket.driverId}'" else "NULL"}, 
                    ${if (ticket.conductorId != null) "'${ticket.conductorId}'" else "NULL"}, 
                    ${if (ticket.stFromId != null) "'${ticket.stFromId}'" else "NULL"}, 
                    ${ticket.stFromVer ?: "NULL"}, 
                    ${if (ticket.stToId != null) "'${ticket.stToId}'" else "NULL"}, 
                    ${ticket.stToVer ?: "NULL"}, 
                    ${if (ticket.aId != null) "'${ticket.aId}'" else "NULL"}, 
                    ${ticket.aVer ?: "NULL"}, 
                    ${if (ticket.waId != null) "'${ticket.waId}'" else "NULL"}, 
                    ${ticket.waVer ?: "NULL"}, 
                    '${ticket.trxId}', 
                    ${ticket.amount}, 
                    '${ticket.terminalSerial}', 
                    ${if (ticket.tid != null) "'${ticket.tid}'" else "NULL"}, 
                    ${ticket.shiftNum}, 
                    ${ticket.ern}, 
                    ${if (ticket.tags != null) "'${ticket.tags}'" else "NULL"}, 
                    '${ticket.terminalId}', 
                    ${if (ticket.cashierId != null) "'${ticket.cashierId}'" else "NULL"}, 
                    '${ticket.payMethodType}'
                )
            """.trimIndent()
            
            entityTemplate.databaseClient.sql(sql).then().block()
        }
    }
}
