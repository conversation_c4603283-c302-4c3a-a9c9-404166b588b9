package ru.sbertroika.passenger.tkp3.transaction.loader.output.model

import java.util.Date
import java.util.UUID

// Origin passenger-transaction-loader:ru.sbertroika.tkp3.export.api.model
data class PassengerTransactionModel(
    /**
     * Идентификатор транзакции
     */
    var id: String,

    /**
     * Регион
     */
    var region: String,

    /**
     * Идентификатор носителя
     */
    var carrierId: String? = null,

    /**
     * Тип носителя
     */
    var carrierType: String? = null,

    /**
     * Время проведения транзаеции
     * Format: ISO_INSTANT
     */
    var createdAt: String? = null,

    /**
     * Серийный номер терминала
     */
    var terminalSerialNumber: String? = null,

    /**
     * Серия билета
     */
    var ticketSeries: String? = null,

    /**
     * Номер билета
     */
    var ticketNumber: String? = null,

    /**
     * Кол-во билетов
     */
    var ticketsCount: Int? = null,

    /**
     * Стоимость
     */
    var amount: Long? = null,

    /**
     * Статус транзакции
     * @see OperationStatus
     */
    var status: Int? = null,

    /**
     * Код статуса транзакции
     */
    var statusReasonCode: Int? = null,

    /**
     * Тип транспорта
     * @see TransportType
     */
    var transportTypeId: Int? = null,

    /**
     * Номер маршрута
     */
    var routeNumber: String? = null,

    /**
     * Название маршрута
     */
    var routeName: String? = null,

    /**
     * Дата и время проезда
     * Format: ISO_INSTANT
     */
    var rideDateTime: String? = null,

    /**
     * Идентификатор остановки входа
     */
    var enterStationId: Int? = null,

    /**
     * Идентификатор остановки выхода
     */
    var exitStationId: Int? = null,

    /**
     * Название остановки Входа
     */
    var enterStationName: String? = null,

    /**
     * Название остановки Выхода
     */
    var exitStationName: String? = null,

    /**
     * Дата и время входа
     */
    var enterDateTime: Date? = null,

    /**
     * Дата и время выхода
     */
    var exitDateTime: Date? = null,

    /**
     * Идентификатор абонемента
     */
    var abonementId: Int? = null,

    /**
     * Тип абонементы
     * @see ru.sbertroika.passenger.tkp3.transaction.loader.input.model.AbonementType
     */
    var abonementType: Int? = null,

    /**
     * Название абонемента
     * @see ru.sbertroika.passenger.tkp3.transaction.loader.input.model.AbonementType
     */
    var abonementName: String? = null,

    /**
     * Идентификатор проекта
     * @see ru.sbertroika.passenger.tkp3.transaction.loader.input.model.AbonementType
     */
    var projectId: UUID? = null
)