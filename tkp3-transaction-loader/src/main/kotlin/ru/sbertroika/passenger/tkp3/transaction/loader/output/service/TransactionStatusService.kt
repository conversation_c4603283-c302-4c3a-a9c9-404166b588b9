package ru.sbertroika.passenger.tkp3.transaction.loader.output.service

import arrow.core.Either
import org.apache.kafka.clients.producer.ProducerRecord
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.ProducerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.passenger.tkp3.transaction.loader.input.model.EmvTrxAuth
import ru.sbertroika.passenger.tkp3.transaction.loader.input.model.EmvTrxDeclined
import ru.sbertroika.passenger.tkp3.transaction.loader.output.clickhouse.TicketClickHouseService
import ru.sbertroika.passenger.tkp3.transaction.loader.output.exceptions.TicketNotFound
import ru.sbertroika.passenger.tkp3.transaction.loader.output.model.OperationStatus
import ru.sbertroika.passenger.tkp3.transaction.loader.output.model.TransactionStatusModel
import ru.sbertroika.passenger.tkp3.transaction.loader.util.mapper
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*

@Service
class TransactionStatusService(
    kafkaProducerFactory: ProducerFactory<String, Any>,
    private val ticketClickHouseService: TicketClickHouseService,
    private val regionMappingService: RegionMappingService,

    @Value("\${spring.kafka.passenger_transaction_status_in_topic}")
    val passengerTransactionStatusInTopic: String,
) {
    private val producer = kafkaProducerFactory.createProducer()
    private val mapper = mapper()
    private val logger = LoggerFactory.getLogger(TransactionService::class.java)

    suspend fun sendTransactionStatus(emvTrxAuth: EmvTrxAuth): Either<Throwable, Unit> = Either.catch {
        sendTransactionStatus(emvTrxAuth.trxId!!, OperationStatus.SUCCESS, emvTrxAuth.createdAt!!)
    }

    suspend fun sendTransactionStatus(emvTrxDeclined: EmvTrxDeclined): Either<Throwable, Unit> = Either.catch {
        sendTransactionStatus(emvTrxDeclined.trxId!!, OperationStatus.FAILED, emvTrxDeclined.createdAt!!)
    }

    private suspend fun sendTransactionStatus(trxId: UUID, status: OperationStatus, createdAt: ZonedDateTime) {
        val ticket = ticketClickHouseService.findByTrxId(trxId)
            .fold(
                { error ->
                    logger.error("Error fetching Ticket for trxId: ${trxId}, error: ${error.message}", error)
                    throw error
                },
                { ticket ->
                    if (ticket == null) {
                        throw TicketNotFound(trxId.toString())
                    }
                    ticket
                }
            )

        val transactionStatus = TransactionStatusModel(
            id = trxId.toString(),
            status = status.ordinal,
            createdAt = createdAt.format(DateTimeFormatter.ISO_INSTANT),
            region = regionMappingService.getRegion(ticket.projectId),
            projectId = ticket.projectId.toString(),
        )

        val out = ProducerRecord<String, Any>(
            passengerTransactionStatusInTopic,
            trxId.toString(),
            mapper.writeValueAsString(transactionStatus)
        )
        producer.send(out)
    }
}