package ru.sbertroika.passenger.tkp3.transaction.loader.output.clickhouse

import arrow.core.Either
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.domain.Sort
import org.springframework.data.r2dbc.core.R2dbcEntityOperations
import org.springframework.data.relational.core.query.Criteria
import org.springframework.data.relational.core.query.Query
import org.springframework.stereotype.Service
import ru.sbertroika.passenger.tkp3.transaction.loader.output.clickhouse.model.EmvTrx
import java.time.format.DateTimeFormatter
import java.util.*

@Service
class EmvTrxClickHouseService(
    @Qualifier("clickhouseR2dbcEntityOperations")
    private val entityTemplate: R2dbcEntityOperations
) {

    suspend fun findByTrxId(trxId: UUID): Either<Throwable, EmvTrx?> = Either.catch {
        val search = mutableListOf<Criteria>()
        search.add(Criteria.where("trx_id").`is`(trxId))

        withContext(Dispatchers.IO) {
            entityTemplate.select(EmvTrx::class.java)
                .matching(
                    Query.query(Criteria.from(search))
                        .sort(Sort.by("createdAt").descending())
                        .limit(1)
                ).all()
                .collectList()
                .block()
                ?.firstOrNull()
        }
    }

    private fun org.springframework.r2dbc.core.DatabaseClient.GenericExecuteSpec.bindNullable(
        name: String, 
        value: Any?
    ): org.springframework.r2dbc.core.DatabaseClient.GenericExecuteSpec {
        return if (value != null) {
            this.bind(name, value)
        } else {
            this.bindNull(name, Any::class.java)
        }
    }

    suspend fun insert(emvTrx: EmvTrx): Either<Throwable, Unit> = Either.catch {
        withContext(Dispatchers.IO) {
            val sql = """
                INSERT INTO emv_trx (
                    trx_id, created_at, record_at, crd_id, terminal_serial, 
                    tid, shift_num, ern, tap_in, tap_out, amount, tags
                ) VALUES (
                    '${emvTrx.trxId}', 
                    '${emvTrx.createdAt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))}', 
                    '${emvTrx.recordAt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))}', 
                    ${if (emvTrx.crdId != null) "'${emvTrx.crdId}'" else "NULL"}, 
                    '${emvTrx.terminalSerial}', 
                    ${if (emvTrx.tid != null) "'${emvTrx.tid}'" else "NULL"}, 
                    ${emvTrx.shiftNum ?: "NULL"}, 
                    ${emvTrx.ern ?: "NULL"}, 
                    ${emvTrx.tapIn}, 
                    ${emvTrx.tapOut}, 
                    ${emvTrx.amount ?: "NULL"}, 
                    ${if (emvTrx.tags != null) "'${emvTrx.tags}'" else "NULL"}
                )
            """.trimIndent()
            
            entityTemplate.databaseClient.sql(sql).then().block()
        }
    }
}
