package ru.sbertroika.passenger.tkp3.transaction.loader.config

import io.r2dbc.postgresql.codec.EnumCodec
import io.r2dbc.spi.ConnectionFactories
import io.r2dbc.spi.ConnectionFactory
import io.r2dbc.spi.ConnectionFactoryOptions
import io.r2dbc.spi.Option
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.data.r2dbc.convert.R2dbcCustomConversions
import org.springframework.data.r2dbc.core.DefaultReactiveDataAccessStrategy
import org.springframework.data.r2dbc.core.R2dbcEntityOperations
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.data.r2dbc.dialect.PostgresDialect
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories
import org.springframework.r2dbc.core.DatabaseClient
import ru.sbertroika.tkp3.pro.model.VehicleStatus
import ru.sbertroika.tkp3.pro.model.VehicleType
import ru.sbertroika.tkp3.pro.model.RouteStatus
import ru.sbertroika.tkp3.pro.model.RouteScheme

@Configuration
@EnableR2dbcRepositories(
    basePackages = ["ru.sbertroika.passenger.tkp3.transaction.loader.output.repository"],
    entityOperationsRef = "mainR2dbcEntityOperations"
)
open class MainDataSourceConfiguration(
    @Value("\${spring.r2dbc.url}")
    private val url: String
) {

    @Bean
    @Primary
    @Qualifier("mainConnectionFactory")
    open fun mainConnectionFactory(): ConnectionFactory {
        val options = ConnectionFactoryOptions.parse(url)
            .mutate()
            .option(
                Option.valueOf("extensions"),
                listOf(
                    EnumCodec.builder()
                        .withEnum("vehicle_status", VehicleStatus::class.java)
                        .withEnum("vehicle_type", VehicleType::class.java)
                        .withEnum("route_status", RouteStatus::class.java)
                        .withEnum("route_scheme_type", RouteScheme::class.java)
                        .withRegistrationPriority(EnumCodec.Builder.RegistrationPriority.FIRST)
                        .build()
                )
            )
            .build()
        return ConnectionFactories.get(options)
    }

    @Bean
    @Primary
    @Qualifier("mainR2dbcEntityOperations")
    open fun mainR2dbcEntityOperations(
        @Qualifier("mainConnectionFactory") connectionFactory: ConnectionFactory,
        r2dbcCustomConversions: R2dbcCustomConversions
    ): R2dbcEntityOperations {
        val strategy = DefaultReactiveDataAccessStrategy(PostgresDialect.INSTANCE,
            listOf(
                VehicleStatusConverter(),
                VehicleTypeConverter(),
                RouteStatusConverter(),
                RouteSchemeTypeConverter()
            ))
        val databaseClient = DatabaseClient.builder()
            .connectionFactory(connectionFactory)
            .build()

        return R2dbcEntityTemplate(databaseClient, strategy)
    }

    @Bean
    @Primary
    @Qualifier("mainDatabaseClient")
    open fun mainDatabaseClient(@Qualifier("mainConnectionFactory") connectionFactory: ConnectionFactory): DatabaseClient {
        return DatabaseClient.builder()
            .connectionFactory(connectionFactory)
            .bindMarkers(PostgresDialect.INSTANCE.bindMarkersFactory)
            .namedParameters(true)
            .build()
    }

    @Bean
    open fun r2dbcCustomConversions(databaseClient: DatabaseClient): R2dbcCustomConversions {
        return R2dbcCustomConversions.of(
            PostgresDialect.INSTANCE,
            listOf(
                VehicleStatusConverter(),
                VehicleTypeConverter(),
                RouteStatusConverter(),
                RouteSchemeTypeConverter()
            )
        )
    }
}