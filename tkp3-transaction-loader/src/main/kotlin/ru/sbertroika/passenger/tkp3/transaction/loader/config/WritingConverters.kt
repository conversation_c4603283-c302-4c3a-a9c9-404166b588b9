package ru.sbertroika.passenger.tkp3.transaction.loader.config

import org.springframework.data.convert.WritingConverter
import org.springframework.data.r2dbc.convert.EnumWriteSupport
import ru.sbertroika.tkp3.pro.model.VehicleStatus
import ru.sbertroika.tkp3.pro.model.VehicleType
import ru.sbertroika.tkp3.pro.model.RouteStatus
import ru.sbertroika.tkp3.pro.model.RouteScheme

@WritingConverter
class VehicleStatusConverter: EnumWriteSupport<VehicleStatus>()

@WritingConverter
class VehicleTypeConverter: EnumWriteSupport<VehicleType>()

@WritingConverter
class RouteStatusConverter: EnumWriteSupport<RouteStatus>()

@WritingConverter
class RouteSchemeTypeConverter: EnumWriteSupport<RouteScheme>()