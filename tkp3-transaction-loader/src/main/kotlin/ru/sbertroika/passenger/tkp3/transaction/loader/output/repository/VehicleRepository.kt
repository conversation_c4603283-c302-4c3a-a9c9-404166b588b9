package ru.sbertroika.passenger.tkp3.transaction.loader.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.tkp3.pro.model.Vehicle
import ru.sbertroika.tkp3.pro.model.VehiclePK
import java.util.*

@Repository
interface VehicleRepository : CoroutineCrudRepository<Vehicle, VehiclePK> {

    @Query("""
        SELECT * FROM vehicle 
        WHERE vh_id = :id 
        AND vh_status != 'IS_DELETED'
        ORDER BY vh_version DESC 
        LIMIT 1
    """)
    suspend fun findLatestById(id: UUID): Vehicle?
}
