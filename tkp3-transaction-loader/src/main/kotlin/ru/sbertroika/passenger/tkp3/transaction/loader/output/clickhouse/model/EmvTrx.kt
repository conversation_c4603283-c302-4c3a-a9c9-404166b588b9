package ru.sbertroika.passenger.tkp3.transaction.loader.output.clickhouse.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime
import java.util.*

@Table("emv_trx")
data class EmvTrx(
    @Column("trx_id")
    val trxId: UUID,
    
    @Column("created_at")
    val createdAt: LocalDateTime,
    
    @Column("record_at")
    val recordAt: LocalDateTime,
    
    @Column("crd_id")
    val crdId: UUID?,
    
    @Column("terminal_serial")
    val terminalSerial: String,
    
    @Column("tid")
    val tid: String?,
    
    @Column("shift_num")
    val shiftNum: Int?,
    
    @Column("ern")
    val ern: Int?,
    
    @Column("tap_in")
    val tapIn: Boolean = false,
    
    @Column("tap_out")
    val tapOut: Boolean = false,
    
    @Column("amount")
    val amount: Int?,
    
    @Column("tags")
    val tags: String?
)
