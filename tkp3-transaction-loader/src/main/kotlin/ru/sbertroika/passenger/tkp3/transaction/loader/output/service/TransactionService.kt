package ru.sbertroika.passenger.tkp3.transaction.loader.output.service

import arrow.core.Either
import org.apache.kafka.clients.producer.ProducerRecord
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.ProducerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.common.v1.TransportType
import ru.sbertroika.passenger.tkp3.transaction.loader.output.clickhouse.EmvTrxClickHouseService
import ru.sbertroika.passenger.tkp3.transaction.loader.output.exceptions.EmvTrxNotReady
import ru.sbertroika.passenger.tkp3.transaction.loader.output.model.PassengerTransactionModel
import ru.sbertroika.passenger.tkp3.transaction.loader.output.repository.RouteCrudRepository
import ru.sbertroika.passenger.tkp3.transaction.loader.output.repository.VehicleRepository
import ru.sbertroika.passenger.tkp3.transaction.loader.util.mapper
import ru.sbertroika.tkp3.pro.model.PayMethodType
import ru.sbertroika.tkp3.pro.model.Ticket
import java.time.format.DateTimeFormatter

@Service
class TransactionService(
    private val kafkaProducerFactory: ProducerFactory<String, Any>,
    private val emvTrxClickHouseService: EmvTrxClickHouseService,
    private val regionMappingService: RegionMappingService,
    private val vehicleRepository: VehicleRepository,
    private val routeRepository: RouteCrudRepository,

    @Value("\${spring.kafka.passenger_transaction_in_topic}")
    val passengerTransactionInTopic: String,
) {
    private val producer = kafkaProducerFactory.createProducer()
    private val mapper = mapper()
    private val logger = LoggerFactory.getLogger(TransactionService::class.java)

    suspend fun sendTransaction(ticket: Ticket): Either<Throwable, Unit> = Either.catch {
        val carrierId = emvTrxClickHouseService.findByTrxId(ticket.trxId!!)
            .fold(
                { error ->
                    logger.error("Error fetching EmvTrx for trxId: ${ticket.trxId}, error: ${error.message}", error)
                    throw error
                },
                { emvTrx ->
                    if (emvTrx == null) {
                        // EmvTrx еще не загрузился. Откладываем на 1 минуту
                        throw EmvTrxNotReady(ticket.trxId.toString())
                    }
                    emvTrx.crdId
                }
            )

        val route = if (ticket.routeId != null && ticket.routeVersion != null) {
            routeRepository.findByRouteIdAndVersion(ticket.routeId!!, ticket.routeVersion!!)
        } else {
            null
        }

        val transaction = PassengerTransactionModel(
            id = ticket.trxId.toString(), // поле не читается passenger-transaction-loader'ом
            region = regionMappingService.getRegion(ticket.projectId),
            carrierId = carrierId?.toString(),
            carrierType = when (ticket.payMethodType) { // не использую cassandra модель по причине перехода на clickhouse
                PayMethodType.EMV -> "EMV"
                PayMethodType.TROIKA_TICKET, PayMethodType.TROIKA_WALLET -> "TROIKA"
                PayMethodType.ABT_TICKET, PayMethodType.ABT_WALLET -> "ABT"
                PayMethodType.QR_TICKET, PayMethodType.QR_WALLET -> "QR"
                else -> null
            },
            createdAt = ticket.createdAt?.format(DateTimeFormatter.ISO_INSTANT),
            terminalSerialNumber = ticket.terminalSerial,
            ticketSeries = ticket.ticketSeries,
            ticketNumber = ticket.ticketNumber,
            amount = ticket.amount?.toLong(),
            status = when (ticket.payMethodType) {
                PayMethodType.EMV -> 0
                PayMethodType.TROIKA_WALLET -> 1
                else -> 0
            },
            statusReasonCode = null,
            transportTypeId = ticket.vehicleId
                ?.let { vehicleRepository.findLatestById(it)?.type }
                ?.let { TransportType.valueOf(it.name) }?.ordinal,
            routeNumber = route?.number,
            routeName = route?.name,
            rideDateTime = ticket.createdAt?.format(DateTimeFormatter.ISO_INSTANT),

            enterStationId = null,   // todo требуется изменение типа поля для заполнения
            exitStationId = null,    // todo требуется изменение типа поля для заполнения
            enterStationName = null, // todo требуется изменение типа поля для заполнения
            exitStationName = null,  // todo требуется изменение типа поля для заполнения
            enterDateTime = null,    // todo требуется изменение типа поля для заполнения
            exitDateTime = null,     // todo требуется изменение типа поля для заполнения

            abonementId = null,      // todo не заполняем, след этап (заполняются при carrierType = ABT)
            abonementType = null,    // todo не заполняем, след этап (заполняются при carrierType = ABT)
            abonementName = null,    // todo не заполняем, след этап (заполняются при carrierType = ABT)
            projectId = ticket.projectId
        )

        val out = ProducerRecord<String, Any>(passengerTransactionInTopic, ticket.trxId.toString(), mapper.writeValueAsString(transaction))
        producer.send(out)
    }
}
