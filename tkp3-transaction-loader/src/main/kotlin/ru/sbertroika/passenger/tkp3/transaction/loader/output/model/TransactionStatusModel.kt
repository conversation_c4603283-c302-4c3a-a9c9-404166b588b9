package ru.sbertroika.passenger.tkp3.transaction.loader.output.model

data class TransactionStatusModel(
    /**
     * Идентификатор транзакции
     */
    var id: String,

    /**
     * Статус транзакции
     * @see OperationStatus
     */
    var status: Int? = null,

    /**
     * Дата в формате yyyy-MM-dd'T'HH:mm:ss'Z'
     */
    var createdAt: String?,

    /**
     * Регион
     */
    var region: String?,

    /**
     * Идентификатор проекта
     */
    var projectId: String?,
)