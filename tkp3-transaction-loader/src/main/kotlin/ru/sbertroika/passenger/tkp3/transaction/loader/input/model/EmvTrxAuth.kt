package ru.sbertroika.passenger.tkp3.transaction.loader.input.model

import java.time.ZonedDateTime
import java.util.UUID

// Origin: tkp2_loader, emv-processing
data class EmvTrxAuth(

    /**
     * Идентификатор транзакции (заказа)
     */
    var trxId: UUID? = null,

    /**
     * Уникальный идентификатор операции (поездки) присвоенный в ТПП
     */
    var tppOperationId: String? = null,

    /**
     * Время формирования события ТПП
     */
    var createdAt: ZonedDateTime? = null,

    /**
     * Время формирования транзакции на сервере
     */
    var recordAt: ZonedDateTime? = null,

    /**
     * Сумма корректировки базовой суммы (в копейках)
     */
    var adjustmentAmount: Int? = null,

    /**
     * Финальная (подлежащая авторизации\списанию) сумма операции (в копейках)
     */
    var finalAmount: Int? = null,

    /**
     * PAR, связанный с средством оплаты
     */
    var par: String? = null,

    /**
     * ERN присвоенный ТПП
     */
    var ern: String? = null,

    /**
     * Доавторизация
     */
    var reauth: Boolean? = null,

    /**
     * Ручное дожатие или погашение долга
     */
    var manual: Boolean? = null,

    /**
     * Номер попытки доавторизации (погашения долга)
     */
    var attempt: Int? = null
)