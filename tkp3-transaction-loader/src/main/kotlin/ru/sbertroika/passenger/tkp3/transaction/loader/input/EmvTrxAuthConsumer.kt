package ru.sbertroika.passenger.tkp3.transaction.loader.input

import com.fasterxml.jackson.module.kotlin.readValue
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.slf4j.LoggerFactory
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component
import ru.sbertroika.passenger.tkp3.transaction.loader.input.model.EmvTrxAuth
import ru.sbertroika.passenger.tkp3.transaction.loader.output.exceptions.RegionNotFoundInMap
import ru.sbertroika.passenger.tkp3.transaction.loader.output.exceptions.TicketNotFound
import ru.sbertroika.passenger.tkp3.transaction.loader.output.service.TransactionStatusService
import ru.sbertroika.passenger.tkp3.transaction.loader.util.mapper
import java.time.Duration

@Component
class EmvTrxAuthConsumer(
    private val transactionStatusService: TransactionStatusService
) {
    private val logger = LoggerFactory.getLogger(this::class.java)
    private val mapper = mapper()

    @KafkaListener(
        groupId = "\${spring.kafka.tkp3_transaction_loader_group}",
        topics = ["\${spring.kafka.emv_trx_auth_topic}"]
    )
    fun receive(record: ConsumerRecord<String, String>, acknowledgment: Acknowledgment) = runBlocking {
        val emvTrxAuth = mapper.readValue<EmvTrxAuth>(record.value())

        transactionStatusService.sendTransactionStatus(emvTrxAuth)
            .fold(
                { error ->
                    when (error) {
                        is TicketNotFound -> {
                            logger.info("Ticket not ready for emv_trx_auth [$emvTrxAuth], will retry in 1 minute", error)
                            acknowledgment.nack(Duration.ofMinutes(1))
                        }
                        is RegionNotFoundInMap -> {
                            logger.error("Region not found for emv_trx_auth [$emvTrxAuth], will retry in 2 minute", error)
                            acknowledgment.nack(Duration.ofMinutes(2))
                        }
                        else -> {
                            logger.error("error process emv_trx_auth [$emvTrxAuth], will retry in 2 minute", error)
                            acknowledgment.nack(Duration.ofMinutes(2))
                        }
                    }
                },
                {
                    acknowledgment.acknowledge()
                }
            )
    }
}
