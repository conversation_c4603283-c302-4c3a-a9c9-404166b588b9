package ru.sbertroika.passenger.tkp3.transaction.loader.output.service

import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.ResourceLoader
import org.springframework.stereotype.Service
import ru.sbertroika.passenger.tkp3.transaction.loader.output.model.RegionMapping
import ru.sbertroika.passenger.tkp3.transaction.loader.output.exceptions.RegionNotFoundInMap
import ru.sbertroika.passenger.tkp3.transaction.loader.util.mapper
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths
import org.slf4j.LoggerFactory
import java.util.SortedMap
import java.util.UUID
import javax.annotation.PostConstruct

@Service
class RegionMappingService(
    @Value("\${regions.file.path}")
    private val regionsFilePath: String,
    private val resourceLoader: ResourceLoader
) {
    private val logger = LoggerFactory.getLogger(RegionMappingService::class.java)
    private val mapper = mapper()
    
    private var projectToRegion: SortedMap<UUID, Int> = sortedMapOf()

    @PostConstruct
    fun init() {
        loadRegions()
    }

    fun getRegion(projectId: UUID?): String {
        if (projectId == null) return ""
        return projectToRegion[projectId]?.toString() 
            ?: throw RegionNotFoundInMap(projectId)
    }

    private fun loadRegions() {
        try {
            val content = if (regionsFilePath.startsWith("classpath:")) {
                // Загружаем из classpath
                val resource = resourceLoader.getResource(regionsFilePath)
                if (!resource.exists()) {
                    logger.warn("Regions file not found at: $regionsFilePath")
                    return
                }
                resource.inputStream.bufferedReader().use { it.readText() }
            } else {
                // Загружаем из файловой системы
                val file = File(regionsFilePath)
                if (!file.exists()) {
                    logger.warn("Regions file not found at: $regionsFilePath")
                    return
                }
                Files.readString(Paths.get(regionsFilePath))
            }

            val regionMappings = mapper.readValue<List<RegionMapping>>(content)
            
            projectToRegion = sortedMapOf<UUID, Int>().apply { 
                regionMappings.forEach { mapping -> 
                    put(UUID.fromString(mapping.projectId), mapping.regionCode) 
                }
            }
            
            logger.info("Loaded ${projectToRegion.size} region mappings")
        } catch (e: Exception) {
            logger.error("Failed to load regions file: $regionsFilePath", e)
        }
    }
}
