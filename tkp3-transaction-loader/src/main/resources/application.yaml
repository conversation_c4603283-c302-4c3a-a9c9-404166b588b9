spring:
  application:
    name: tkp3-transaction-loader
  main:
    allow-bean-definition-overriding: true

  clickhouse:
    url: r2dbc:clickhouse:${CLICKHOUSE_URL://admin:admin@localhost:8123/tkp3_transaction_loader}

  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://postgres:postgres@localhost:5432/tkp3_transaction_loader}

  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092;localhost:9093}
    listener:
      ack-mode: manual
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    pro_internal_ticket_topic: ${PRO_INTERNAL_TICKET_TOPIC:PRO.INTERNAL.TICKET}
    emv_trx_auth_topic: ${EMV_TRX_AUTH_TOPIC:EMV.TRX.AUTH}
    emv_trx_declined_topic: ${EMV_TRX_DECLINED_TOPIC:EMV.TRX.DECLINED}
    passenger_transaction_in_topic: ${PASSENGER_TRANSACTION_IN_TOPIC:PASSENGER.TRANSACTION.IN}
    passenger_transaction_status_in_topic: ${PASSENGER_TRANSACTION_STATUS_IN_TOPIC:PASSENGER.TRANSACTION.STATUS.IN}
    tkp3_transaction_loader_group: ${TKP3_TRANSACTION_LOADER_GROUP:tkp3_transaction_loader_group}

regions:
  file:
    path: ${REGIONS_FILE_PATH:regions.json}