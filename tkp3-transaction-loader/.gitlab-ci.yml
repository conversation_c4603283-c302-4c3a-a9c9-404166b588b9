include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/java-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates

# ========================
# tkp3-transaction-loader: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
tkp3_transaction_loader_build_develop:
  stage: build
  needs: [develop_build_test_publish]
  variables:
    SERVICE_NAME: "tkp3-transaction-loader"
    TAG: "$CI_COMMIT_SHORT_SHA"
  extends:
    - .docker_gradle_build_and_push
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - tkp3-transaction-loader/**
        - ./**

tkp3_transaction_loader_helm_kubeval_testing_develop:
  stage: test
  needs: 
    - job: tkp3_transaction_loader_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    SERVICE_NAME: "tkp3-transaction-loader"
  extends:
    - .validate_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - tkp3-transaction-loader/**
        - charts/tkp3-transaction-loader/**

tkp3_transaction_loader_deploy_chart_develop:
  stage: deploy
  needs:
    - tkp3_transaction_loader_helm_kubeval_testing_develop
    - job: tkp3_transaction_loader_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    STAGE: "dev"
    SERVICE_NAME: "tkp3-transaction-loader"
  extends:
    - .deploy_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - tkp3-transaction-loader/**
        - charts/tkp3-transaction-loader/**

# --- TAG ---
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - tkp3-transaction-loader/**

tkp3_transaction_loader_build_tag:
  stage: build
  variables:
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "tkp3-transaction-loader"
  extends:
    - .docker_gradle_build_and_push
  <<: *tag_rules

tkp3_transaction_loader_helm_kubeval_testing_tag:
  stage: test
  needs:
    - tkp3_transaction_loader_build_tag
  variables:
    SERVICE_NAME: "tkp3-transaction-loader"
  extends:
    - .validate_helm_template
  <<: *tag_rules

tkp3_transaction_loader_deploy_chart_tag:
  stage: deploy
  needs:
    - tkp3_transaction_loader_helm_kubeval_testing_tag
  variables:
    STAGE: "dev"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "tkp3-transaction-loader"
  extends:
    - .deploy_helm_template
  <<: *tag_rules

tkp3_transaction_loader_deploy_prod:
  stage: deploy
  needs:
    - tkp3_transaction_loader_deploy_chart_tag
  variables:
    STAGE: "prod"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "tkp3-transaction-loader"
    NAMESPACE: "$NAMESPACE-prod"
    KUBECONFIG_FILE: $KUBECONFIG_CCE_PROD
  extends:
    - .deploy_helm_template
  when: manual
  <<: *tag_rules
