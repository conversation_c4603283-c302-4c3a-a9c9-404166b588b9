# TKP3 Transaction Loader

## Переменные окружения

### ClickHouse
- `CLICKHOUSE_URL` - URL подключения к ClickHouse (по умолчанию: `admin:admin@localhost:8123/tkp3_transaction_loader`)

### R2DBC
- `R2DB_URL` - URL подключения к базе данных через R2DBC (по умолчанию: `postgresql://postgres:postgres@localhost:5432/tkp3_transaction_loader`)

### Kafka топики
- `PRO_INTERNAL_TICKET_TOPIC` - топик для внутренних тикетов (по умолчанию: `PRO.INTERNAL.TICKET`)
- `EMV_TRX_AUTH_TOPIC` - топик для авторизованных EMV транзакций (по умолчанию: `EMV.TRX.AUTH`)
- `EMV_TRX_DECLINED_TOPIC` - топик для отклоненных EMV транзакций (по умолчанию: `EMV.TRX.DECLINED`)
- `PASSENGER_TRANSACTION_IN_TOPIC` - топик для входящих пассажирских транзакций (по умолчанию: `PASSENGER.TRANSACTION.IN`)
- `PASSENGER_TRANSACTION_STATUS_IN_TOPIC` - топик для статусов пассажирских транзакций (по умолчанию: `PASSENGER.TRANSACTION.STATUS.IN`)

### Kafka группа
- `TKP3_TRANSACTION_LOADER_GROUP` - ID группы потребителей Kafka (по умолчанию: `tkp3_transaction_loader_group`)

### Regions
- `REGIONS_FILE_PATH` - путь к файлу конфигурации регионов (по умолчанию: `regions.json`)

#### Пример файла regions.json:
```json
[
  {
    "projectId": "550e8400-e29b-41d4-a716-************",
    "regionCode": 77
  },
  {
    "projectId": "550e8400-e29b-41d4-a716-************", 
    "regionCode": 78
  },
  {
    "projectId": "550e8400-e29b-41d4-a716-************",
    "regionCode": 50
  },
  {
    "projectId": "550e8400-e29b-41d4-a716-************",
    "regionCode": 47
  },
  {
    "projectId": "550e8400-e29b-41d4-a716-************",
    "regionCode": 36
  }
]
```

Где:
- `projectId` - уникальный идентификатор проекта (UUID)
- `regionCode` - код региона (числовой код субъекта РФ)
