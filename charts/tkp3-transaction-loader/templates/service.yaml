apiVersion: v1
kind: Service
metadata:
  name: {{ include "tkp3-transaction-loader.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "tkp3-transaction-loader.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.http.type }}
  ports:
    - port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "tkp3-transaction-loader.selectorLabels" . | nindent 4 }}
