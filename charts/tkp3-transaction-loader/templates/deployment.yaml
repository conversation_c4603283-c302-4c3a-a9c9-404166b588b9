apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "tkp3-transaction-loader.fullname" . }}
  namespace: {{ .Values.namespace | default (include "tkp3-transaction-loader.fullname" .) }}
  labels:
    {{- include "tkp3-transaction-loader.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "tkp3-transaction-loader.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "tkp3-transaction-loader.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "tkp3-transaction-loader.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command: ["java"]
          args: ["-Djava.security.egd=file:/dev/./urandom", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005", "-jar", "tkp3-transaction-loader.jar"]
          env:
            # Client logging configuration
            - name: CLIENT_LOGGING_ENABLE
              value: {{ .Values.env.client.logging.enable | quote }}
            
            # Database URLs from secrets
            - name: CLICKHOUSE_URL
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.secrets.clickhouseUrl.secretName }}
                  key: {{ .Values.secrets.clickhouseUrl.key }}
            - name: R2DB_URL
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.secrets.r2dbUrl.secretName }}
                  key: {{ .Values.secrets.r2dbUrl.key }}
            
            # Kafka configuration
            - name: KAFKA_SERVERS
              value: {{ .Values.env.kafka.servers | quote }}
            
            # Zookeeper configuration
            - name: ZOOKEEPER_NODES
              value: {{ .Values.env.zookeeper.nodes | quote }}
            
            # Regions file path
            - name: REGIONS_FILE_PATH
              value: "/app/config/regions.json"
            
            # Kafka topics (using default values from application.yaml)
            - name: PRO_INTERNAL_TICKET_TOPIC
              value: "PRO.INTERNAL.TICKET"
            - name: EMV_TRX_AUTH_TOPIC
              value: "EMV.TRX.AUTH"
            - name: EMV_TRX_DECLINED_TOPIC
              value: "EMV.TRX.DECLINED"
            - name: PASSENGER_TRANSACTION_IN_TOPIC
              value: "PASSENGER.TRANSACTION.IN"
            - name: PASSENGER_TRANSACTION_STATUS_IN_TOPIC
              value: "PASSENGER.TRANSACTION.STATUS.IN"
            - name: TKP3_TRANSACTION_LOADER_GROUP
              value: "tkp3_transaction_loader_group"
          
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.http.targetPort }}
              protocol: TCP
          
          volumeMounts:
            - name: regions-config
              mountPath: /app/config
              readOnly: true
          
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      
      volumes:
        - name: regions-config
          configMap:
            name: {{ include "tkp3-transaction-loader.fullname" . }}-config
      
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
