# Default values for tkp3-transaction-loader.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

application: tkp3-transaction-loader
namespace: ""

replicaCount: 1

image:
  repository: tkp3-transaction-loader
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "dev"

imagePullSecrets:
  - name: "default-secret"
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
# fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  http:
    type: ClusterIP
    port: 8080
    targetPort: 8080

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 200m
    memory: 256Mi

nodeSelector: {}

tolerations: []

affinity: {}

# Database secrets - these should reference Kubernetes secrets
secrets:
  clickhouseUrl:
    secretName: "db-secrets"
    key: "clickhouseUrl"
  r2dbUrl:
    secretName: "db-secrets"
    key: "r2url.pro"

env:
  client:
    logging:
      enable: true
  kafka:
    servers: "**********:9092;***********:9092;**********:9092"
  zookeeper:
    nodes: "***********:2181,**********:2181,***********:2181"

config:
  regionsJson: |
    [
      {
        "name": "Республика Ингушетия",
        "code": "06"
      },
      {
        "name": "Владимирская область",
        "code": "33"
      },
      {
        "name": "Воронежская область",
        "code": "36"
      },
      {
        "name": "Костромская область",
        "code": "44"
      },
      {
        "name": "Пермский край",
        "code": "59"
      },
      {
        "name": "Ростовская область",
        "code": "61"
      },
      {
        "name": "Ямало-Ненецкий автономный округ",
        "code": "89"
      },
      {
        "name": "Томская область",
        "code": "70"
      },
      {
        "name": "Республика Карелия",
        "code": "10"
      },
      {
        "name": "Кемеровская область",
        "code": "42"
      },
      {
        "name": "Республика Бурятия",
        "code": "03"
      },
      {
        "name": "Кабардино-Балкарская Республика",
        "code": "07"
      },
      {
        "name": "Республика Алтай",
        "code": "04"
      },
      {
        "name": "Новосибирская область",
        "code": "54"
      },
      {
        "name": "Новосибирская область test",
        "code": "54test"
      },
      {
        "name": "Республика Саха (Якутия)",
        "code": "14"
      },
      {
        "name": "Курская область",
        "code": "46"
      },
      {
        "name": "Московская область",
        "code": "50"
      },
      {
        "name": "Саратовская область",
        "code": "64"
      },
      {
        "name": "Пензенская область",
        "code": "58"
      },
      {
        "name": "Ставропольский край",
        "code": "26"
      },
      {
        "name": "Республика Тыва",
        "code": "17"
      },
      {
        "name": "Удмуртская Республика",
        "code": "18"
      },
      {
        "name": "Республика Хакасия",
        "code": "19"
      },
      {
        "name": "Тульская область",
        "code": "71"
      },
      {
        "name": "Архангельская область",
        "code": "29"
      },
      {
        "name": "Республика Марий Эл",
        "code": "12"
      },
      {
        "name": "Ленинградская область",
        "code": "47"
      },
      {
        "name": "Нижневартовск",
        "code": "86"
      },
      {
        "name": "Сургут",
        "code": "86s"
      }
    ]
