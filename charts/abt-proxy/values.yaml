# Default values for abt-proxy.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

application: abt-proxy
namespace: ""

replicaCount: 1

image:
  repository: abt-proxy
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "dev"

imagePullSecrets:
  - name: "default-secret"
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: { }

podSecurityContext: { }
# fsGroup: 2000

securityContext: { }
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  http:
    type: ClusterIP
    port: 8080
    targetPort: 8080
  grpc:
    type: ClusterIP
    port: 5000
    targetPort: 5000

resources:
  limits:
    cpu: 1000m
    memory: 256Mi
  requests:
    cpu: 100m
    memory: 128Mi

nodeSelector: { }

tolerations: [ ]

affinity: { }

env:
  redis:
    host: "redis.ext.svc.cluster.local"
    port: 6379
  service:
    abt_emission_url: "abt-emission.abt.svc.cluster.local:5000"
    sdbp_gate_url: "sdbp-connector.ext.svc.cluster.local:5000"
  regions: "54,64"