apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "abt-proxy.fullname" . }}
  namespace: {{ .Values.namespace | default (include "abt-proxy.fullname" .) }}
  labels:
    {{- include "abt-proxy.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "abt-proxy.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "abt-proxy.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "abt-proxy.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command: ["java"]
          args: ["-Djava.security.egd=file:/dev/./urandom", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005", "-jar", "abt-proxy.jar"]
          env:
            - name: CLIENT_LOGGING_ENABLE
              value: "true"
            - name: REDIS_HOST
              value: {{ .Values.env.redis.host }}
            - name: REDIS_PORT
              value: {{ .Values.env.redis.port | quote }}
            - name: ABT_EMISSION_URL
              value: {{ .Values.env.service.abt_emission_url }}
            - name: SDBP_GATE_URL
              value: {{ .Values.env.service.sdbp_gate_url }}
            - name: REGIONS
              value: {{ .Values.env.regions }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: grpc
              containerPort: {{ .Values.service.grpc.targetPort }}
              protocol: TCP
            - name: http
              containerPort: {{ .Values.service.http.targetPort }}
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
