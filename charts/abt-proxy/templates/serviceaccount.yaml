{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "abt-proxy.serviceAccountName" . }}
  namespace: {{ .Values.namespace | default (include "abt-proxy.fullname" .) }}
  labels:
    {{- include "abt-proxy.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
