# GitLab CI/CD Pipeline для passenger-domain (shell executor + docker run)

include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/main-pipeline-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates
  - local: '/abt-proxy/.gitlab-ci.yml'
    rules:
      - if: $CI_PIPELINE_SOURCE == "merge_request_event"
        when: never
      - when: always
  - local: '/tkp3-transaction-loader/.gitlab-ci.yml'
    rules:
      - if: $CI_PIPELINE_SOURCE == "merge_request_event"
        when: never
      - when: always

variables:
  GRADLE_OPTS: "-Dorg.gradle.daemon=false"
  GRADLE_USER_HOME: "$CI_PROJECT_DIR/.gradle"
  DOCKER_BASE_IMAGE: "gradle:8.5-jdk17"
  KUBE_NAMESPACE: "passenger"

cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - .gradle/wrapper
    - .gradle/caches
    - .gradle/daemon

stages:
  - publish-snapshot
  - build
  - test
  - deploy
  - release

.docker_gradle: &docker_gradle
  before_script:
    - echo "Docker base image $DOCKER_BASE_IMAGE"
    - export DOCKER_GRADLE_CACHE="$PWD/.gradle"
    - mkdir -p "$DOCKER_GRADLE_CACHE"
    - chmod -R 777 "$DOCKER_GRADLE_CACHE" || true
    - chmod +x scripts/setup-gradle-auth.sh
#  after_script:
#    - chmod -R 777 "$PWD/.gradle" || true
#    - chmod -R 777 "$PWD/build" || true

# ========================
# 1. ЛЮБАЯ ВЕТКА (кроме develop/master): build + test
# ========================
build_and_test:
  <<: *docker_gradle
  stage: test
  extends:
    - .gradle_build_new_template
  artifacts:
    paths:
      - build/
      - "**/build/reports/"
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_REF_NAME != "develop" && $CI_COMMIT_REF_NAME != "master" && $CI_COMMIT_TAG == null
# ========================
# 2. DEVELOP: build + test + publish SNAPSHOT (одним шагом)
# ========================

develop_build_test_publish:
  <<: *docker_gradle
  stage: publish-snapshot
  extends:
    - .gradle_build_test_publish_template
  artifacts:
    paths:
      - build/
      - "**/build/reports/"
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
  environment:
    name: develop
    url: https://nexus.sbertroika.tech/repository/maven-snapshots/

# ========================
# 3. MASTER: только release (без отдельного build/test)
# ========================

release:
  <<: *docker_gradle
  stage: release
  tags:
    - docker
  before_script:
    - |
      if echo "$CI_COMMIT_MESSAGE" | grep -q "^\[Gradle Release Plugin\]"; then
        echo "Gradle Release Plugin commit detected, skipping job."
        exit 0
      fi
    - export DOCKER_GRADLE_CACHE="$PWD/.gradle"
    - mkdir -p "$DOCKER_GRADLE_CACHE"
    - chmod -R 777 "$DOCKER_GRADLE_CACHE" || true
    - chmod +x scripts/setup-gradle-auth.sh
    - chown -R $(id -u):$(id -g) .git/ || true
    - chmod -R 755 .git/ || true
    - rm -f .git/index.lock
    - eval $(ssh-agent -s)
    - ssh-add ~/.ssh/id_rsa || echo "SSH key not found, trying alternative locations"
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H git.sbertroika.ru >> ~/.ssh/known_hosts
  script:
    - echo "Выпуск релиза версии"
    - >
      docker run --rm
      -v "$PWD":/workspace
      -v "$PWD/.gradle":/tmp/.gradle
      -v "$SSH_AUTH_SOCK":/tmp/ssh_agent.sock
      -e SSH_AUTH_SOCK=/tmp/ssh_agent.sock
      -e MAVEN_USER="$MAVEN_USER"
      -e MAVEN_PASSWORD="$MAVEN_PASSWORD"
      -e GRADLE_USER_HOME="/tmp/.gradle"
      -w /workspace $DOCKER_BASE_IMAGE bash -c "
        mkdir -p /root/.ssh
        chmod 700 /root/.ssh
        ssh-keyscan -H git.sbertroika.ru >> /root/.ssh/known_hosts
        mkdir -p /home/<USER>/.ssh
        chmod 700 /home/<USER>/.ssh
        ssh-keyscan -H git.sbertroika.ru >> /home/<USER>/.ssh/known_hosts
        export GIT_SSH_COMMAND='ssh -o UserKnownHostsFile=/root/.ssh/known_hosts'
        ./scripts/setup-gradle-auth.sh
        git config --global --add safe.directory /workspace
        git config --global user.name 'GitLab CI'
        git config --global user.email '<EMAIL>'
        git remote set-<NAME_EMAIL>:tkp3/passenger/passenger-domain.git
        git fetch origin master
        git checkout -B master origin/master
        gradle release --no-daemon --info
        chmod -R 777 /workspace
        chmod -R 777 /tmp/.gradle
      "
  artifacts:
    paths:
      - build/
      - "**/build/reports/"
    expire_in: 1 week
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master" && $CI_COMMIT_MESSAGE =~ /^\[Gradle Release Plugin\]/'
      when: never # Skip if we have "gradle release" in commit message
    - if: '$CI_COMMIT_BRANCH == "master" && $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never  # Skip during MR creation/update
    - if: $CI_COMMIT_REF_NAME == "master" && $CI_PIPELINE_SOURCE == "push"
      when: on_success  # Run only after merge (push to master)
  environment:
    name: production
    url: https://nexus.sbertroika.tech/repository/maven-releases/