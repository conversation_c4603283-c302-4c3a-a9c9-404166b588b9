package ru.sbertroika.abt.gate.input

import com.google.protobuf.Empty
import io.grpc.ManagedChannelBuilder
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import ru.sbertroika.abt.gate.v1.*
import java.util.concurrent.TimeUnit

class ABTGateServiceGrpcTest {

    companion object {
        private val server = "localhost:5011"
    }

    @Test
    fun emission(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = ABTGateServiceGrpcKt.ABTGateServiceCoroutineStub(channel)
            val request = EmissionRequest.newBuilder()
                .setNumber("9643102703300250150")
                .setRegion("47")
                .setUid("AD26B9D0")
                .build()
            val response = client.emission(request)
            println("response: $response")
            Assertions.assertFalse(response.hasError())
            Assertions.assertNotNull(response.cardId)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun emissionByType(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = ABTGateServiceGrpcKt.ABTGateServiceCoroutineStub(channel)
            val request = EmissionRequest.newBuilder()
                .setNumber("00400000025")
                .setType("7647ee35-fcd9-4f91-a704-c2b38bd01fad")
                .build()
            val response = client.emission(request)
            println("response: $response")
            Assertions.assertFalse(response.hasError())
            Assertions.assertNotNull(response.cardId)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun abonementInfo(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = ABTGateServiceGrpcKt.ABTGateServiceCoroutineStub(channel)
            val request = AbonementInfoRequest.newBuilder()
                .setId("382054")
                .setRegion("47")
                .setTemplateId("10")
                .build()
            val response = client.abonementInfo(request)
            println("response: $response")
            Assertions.assertFalse(response.hasError())
            Assertions.assertNotNull(response.info)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun abonementByInvoice(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = ABTGateServiceGrpcKt.ABTGateServiceCoroutineStub(channel)
            val request = AbonementByInvoiceRequest.newBuilder()
                .setRegion("47")
                .setInvoiceId("35")
                .build()
            val response = client.abonementByInvoice(request)
            println("response: $response")
            Assertions.assertFalse(response.hasError())
            Assertions.assertNotNull(response.info)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun abonementList(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = ABTGateServiceGrpcKt.ABTGateServiceCoroutineStub(channel)
            val request = AbonementListRequest.newBuilder()
                .setCarrierId("29d6619e-9033-4e39-a73a-21a446883263")
                .build()
            val response = client.abonementList(request)
            println("response: $response")
            Assertions.assertFalse(response.hasError())
            Assertions.assertNotNull(response.abonements)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }

    @Test
    fun cardTypes(): Unit = runBlocking {
        val channel = ManagedChannelBuilder.forTarget(server).usePlaintext().build()
        try {
            val client = ABTGateServiceGrpcKt.ABTGateServiceCoroutineStub(channel)
            val response = client.cardTypes(Empty.getDefaultInstance())
            println("response: $response")
            Assertions.assertFalse(response.hasError())
            Assertions.assertNotNull(response.types)
        } catch (ex: Exception) {
            println("ex: $ex")
            Assertions.fail()
        } finally {
            channel
                .shutdown()
                .awaitTermination(5000, TimeUnit.MILLISECONDS)
        }
    }
}