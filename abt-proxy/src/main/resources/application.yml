spring:
  application:
    name: abt-proxy

  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}

abt_emission_url: ${ABT_EMISSION_URL:localhost:5008}
sdbp_gate_url: ${SDBP_GATE_URL:localhost:6000}

regions: ${REGIONS:"47;40;27"}
request_to_region_timeout: ${REQUEST_TO_REGION_TIMEOUT:10000} # 5mins
region_abonements_cache_timeout: ${REGION_ABONEMENTS_CACHE_TIMEOUT:5} # 5mins

grpc:
  port: 5000
server:
  port: 8080