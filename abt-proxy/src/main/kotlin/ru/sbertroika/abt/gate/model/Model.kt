package ru.sbertroika.abt.gate.model

data class Abonement(
    val id: String,
    val name: String,
    val templateId: String,
    val typeValue: Int,
    val description: String,
    val balance: Int,
    val region: String,
    val startDate: Long,
    val endDate: Long,
    val isActive: Boolean,
    val isBlocked: Boolean,
)

data class WalletInfo(
    val walletId: Long,
    val cardId: Int?,
    val name: String,
    val description: String?,
    val agentId: Long?,
    val pan: String?,
    val panHash: String?,
    val uid: String?,
    val uidHash: String?,
    val isActive: Boolean,
    val balance: Long,
    val minReplenishment: Int,
    val maxReplenishment: Int,
    val number: String?
)
