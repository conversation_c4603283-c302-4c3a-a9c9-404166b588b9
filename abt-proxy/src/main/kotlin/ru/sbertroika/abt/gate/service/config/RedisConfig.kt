package ru.sbertroika.abt.gate.service.config

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.springframework.beans.factory.annotation.Value
import org.springframework.cache.annotation.EnableCaching
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.redis.connection.RedisConnectionFactory
import org.springframework.data.redis.connection.RedisStandaloneConfiguration
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer
import org.springframework.data.redis.serializer.StringRedisSerializer
import ru.sbertroika.abt.gate.model.Abonement


@EnableCaching
@Configuration
class RedisConfig(@Value("\${spring.data.redis.host}") private val redisHost: String,
                  @Value("\${spring.data.redis.port}") private val redisPort: Int) {

    @Bean
    fun redisConnectionFactory(): JedisConnectionFactory =
        JedisConnectionFactory(RedisStandaloneConfiguration(
            redisHost,
            redisPort))

    @Bean
    fun redisTemplateAbonements(redisConnectionFactory: RedisConnectionFactory): RedisTemplate<String, Abonement> {
        val redisTemplate = RedisTemplate<String, Abonement>()
        redisTemplate.setConnectionFactory(redisConnectionFactory)
        redisTemplate.keySerializer = StringRedisSerializer()
        redisTemplate.valueSerializer = Jackson2JsonRedisSerializer(jacksonObjectMapper(), Abonement::class.java)
        return redisTemplate
    }
}