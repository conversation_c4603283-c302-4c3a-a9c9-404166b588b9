package ru.sbertroika.abt.gate.input

import com.google.protobuf.Empty
import org.apache.commons.logging.LogFactory
import org.lognet.springboot.grpc.GRpcService
import ru.sbertroika.abt.gate.service.ABTEmissionService
import ru.sbertroika.abt.gate.service.SDBPService
import ru.sbertroika.abt.gate.v1.*
import ru.sbertroika.common.NotFound
import ru.sbertroika.common.toOperationError
import ru.sbertroika.common.v1.AbonementType
import ru.sbertroika.common.v1.ErrorType
import ru.sbertroika.common.v1.OperationError

@GRpcService
class ABTGateServiceGrpc(
    private val abtEmissionService: ABTEmissionService,
    private val sdbpService: SDBPService
) : ABTGateServiceGrpcKt.ABTGateServiceCoroutineImplBase() {

    private val log = LogFactory.getLog(this::class.java)

    override suspend fun emission(request: EmissionRequest): EmissionResponse {
        if (!request.type.isNullOrEmpty()) {
            return abtEmissionService.makeCard(request.number, request.type).fold(
                { e1 ->
                    EmissionResponse.newBuilder()
                        .setError(operationError(e1))
                },
                { makeResult ->
                    sdbpService.getCardInfoByNum(makeResult.cardNum, makeResult.region).fold(
                        { e2 ->
                            if (e2 is NotFound) {
                                EmissionResponse.newBuilder()
                                    .setError(
                                        OperationError.newBuilder()
                                            .setType(ErrorType.NOT_FOUND)
                                            .setMessage(if (e2.message.isNullOrEmpty()) "" else e2.message)
                                    )
                            } else {
                                EmissionResponse.newBuilder()
                                    .setError(operationError(e2))
                            }
                        },
                        { cardInfo ->
                            abtEmissionService.emission(request.number, cardInfo.uid, request.type, makeResult.region).fold(
                                { e3 ->
                                    EmissionResponse.newBuilder()
                                        .setError(operationError(e3))
                                },
                                { res ->
                                    EmissionResponse.newBuilder()
                                        .setCardId(res)
                                }
                            )
                        }
                    )
                }
            ).build()
        }

        return abtEmissionService.emission(request.number, request.uid, request.type, request.region).fold(
            {
                log.error("Error emission", it)
                EmissionResponse.newBuilder()
                    .setError(
                        operationError(it)
                    )
            },
            { res ->
                EmissionResponse.newBuilder()
                    .setCardId(res)
            }
        ).build()
    }

    override suspend fun abonementInfo(request: AbonementInfoRequest): AbonementInfoResponse {
        return sdbpService.abonementInfo(request.id, request.templateId, request.region).fold(
            {
                log.error("Error abonementInfo, region: ${request.region}, id:${request.id}, invoice ${request.templateId}")
                AbonementInfoResponse.newBuilder()
                    .setError(
                        OperationError.newBuilder()
                            .setType(ErrorType.SERVICE_ERROR)
                            .setMessage(if (it.message.isNullOrEmpty()) "" else it.message)
                            .build()
                    )
            },
            { info ->
                abtEmissionService.emission(info.cardNumber, info.cardUid, "", request.region).fold(
                    {
                        log.error("Error emission by cardNumber:${info.cardNumber}, uid:${info.cardUid}", it)
                        AbonementInfoResponse.newBuilder()
                            .setError(
                                OperationError.newBuilder()
                                    .setType(ErrorType.SERVICE_ERROR)
                                    .setMessage(if (it.message.isNullOrEmpty()) "" else it.message)
                                    .build()
                            )
                    },
                    { carrierId ->
                        AbonementInfoResponse.newBuilder()
                            .setInfo(
                                AbonementInfo.newBuilder()
                                    .setId(info.id)
                                    .setType(info.type)
                                    .setName(info.name)
                                    .setCarrierId(carrierId)
                                    .build()
                            )
                    }
                )
            }
        ).build()
    }

    override suspend fun abonementList(request: AbonementListRequest): AbonementListResponse {
        return abtEmissionService.getCardInfo(request.carrierId)
            .fold({
                log.error("Error getCardInfo, carrierId = ${request.carrierId}")
                AbonementListResponse.newBuilder()
                    .setError(
                        OperationError.newBuilder()
                            .setType(ErrorType.SERVICE_ERROR)
                            .setMessage(if (it.message.isNullOrEmpty()) "" else it.message)
                            .build()
                    )
            }, { info ->
                sdbpService.abonementList(info.uid, info.number).fold(
                    {
                        log.error("Error abonementList", it)
                        AbonementListResponse.newBuilder()
                            .setError(
                                OperationError.newBuilder()
                                    .setType(ErrorType.SERVICE_ERROR)
                                    .setMessage(if (it.message.isNullOrEmpty()) "" else it.message)
                                    .build()
                            )
                    },
                    { list ->
                        AbonementListResponse.newBuilder()
                            .setAbonements(
                                Abonements.newBuilder()
                                    .addAllAbonement(list.map(this::toAbonement).toList())
                                    .build()
                            )
                    })
            }).build()
    }

    override suspend fun abonementByInvoice(request: AbonementByInvoiceRequest): AbonementInfoResponse {
        log.info("abonementByInvoice(), region: ${request.region}, invoice ${request.invoiceId}")
        return sdbpService.abonementByInvoice(
            invoiceId = request.invoiceId,
            region = request.region
        ).fold({
            log.error("Error abonementByInvoice, region: ${request.region}, invoice ${request.invoiceId}", it)
            AbonementInfoResponse.newBuilder()
                .setError(
                    toOperationError(Error(it))
                )
        },
            { info ->
                log.info("abonementByInvoice() success, region: ${request.region}, invoice ${request.invoiceId}")
                AbonementInfoResponse.newBuilder()
                    .setInfo(
                        AbonementInfo.newBuilder()
                            .setId(info.id)
                            .setType(info.type)
                            .setName(info.name)
                            .build()
                    )
            }).build()
    }

    override suspend fun cardTypes(request: Empty): CardTypesResponse {
        return abtEmissionService.getCardTypes().fold(
            {
                log.error("Error cardTypes", it)
                CardTypesResponse.newBuilder()
                    .setError(
                        OperationError.newBuilder()
                            .setType(ErrorType.SERVICE_ERROR)
                            .setMessage(if (it.message.isNullOrEmpty()) "" else it.message)
                            .build()
                    )
            },
            { list ->
                CardTypesResponse.newBuilder()
                    .setTypes(
                        CardTypes.newBuilder()
                            .addAllList(list.map<ru.sbertroika.abt.emission.v1.CardType, CardType?>(this::toCardType).toList())
                    )
            }
        ).build()
    }

    override suspend fun cardInfo(request: CardInfoRequest): CardInfoRequestResponse {
        return abtEmissionService.getCardInfo(request.cardId).fold(
            {
                CardInfoRequestResponse.newBuilder()
                    .setError(
                        OperationError.newBuilder()
                            .setType(ErrorType.SERVICE_ERROR)
                            .setMessage(if (it.message.isNullOrEmpty()) "" else it.message)
                            .build()
                    )
            },
            {
                CardInfoRequestResponse.newBuilder()
                    .setInfo(CardInfo.newBuilder()
                        .setId(it.id)
                        .setNumber(it.number)
                        .setRegion(it.region)
                        //.setType() TODO добавить если нужно
                        .setUid(it.uid)
                        )
            }
        ).build()
    }

    override suspend fun socialCategories(request: SocialCategoriesRequest): SocialCategoriesResponse {
        return sdbpService.getSocialCategories(request.region, request.pan, request.ips).fold(
            {
                log.error("Get payment status error", it)
                SocialCategoriesResponse.newBuilder().setError(
                    toOperationError(Error(it))
                )
            },
            {
                SocialCategoriesResponse.newBuilder().setResult(it)
            }
        ).build()
    }

    override suspend fun walletInfo(request: WalletInfoRequest): WalletInfoResponse {
        log.info("walletInfo start wallet id ${request.id}, region ${request.region}")
        return sdbpService.walletInfo(request.id, request.region).fold({
            log.error("walletInfo error", it)
           WalletInfoResponse.newBuilder()
               .setError(
                   OperationError.newBuilder()
                       .setType(ErrorType.SERVICE_ERROR)
                       .setMessage(if (it.message.isNullOrEmpty()) "" else it.message)
                       .build()
               )
        }, {wi ->
            log.info("walletInfo start return map builder")
            val builder =
                ru.sbertroika.abt.gate.v1.WalletInfo.newBuilder()
            log.info("walletInfo return builder walletId")
            builder.setWalletId(wi.walletId)
            log.info("walletInfo return builder name")
            builder.setName(wi.name)
            log.info("walletInfo return builder balance")
            builder.setBalance(wi.balance)
            log.info("walletInfo return builder pan")
            builder.setPan(wi.pan)
            log.info("walletInfo return builder panHash")
            builder.setPanHash(wi.panHash)
            log.info("walletInfo return builder uid")
            builder.setUid(wi.uid)
            log.info("walletInfo return builder uidHash")
            builder.setUidHash(wi.uidHash)
            log.info("walletInfo return builder description")
            builder.setDescription(wi.description)
            log.info("walletInfo return builder isActive")
            builder.setIsActive(wi.isActive)
            log.info("walletInfo return builder cardId")
            wi.cardId?.let { builder.setCardId(it) }
            log.info("walletInfo return builder agentId")
            wi.agentId?.let { builder.setAgentId(it) }
            log.info("walletInfo end return map builder")
            log.info("walletInfo end")
            WalletInfoResponse.newBuilder().setInfo(builder.build())
        }).build()
    }

    private fun operationError(it: Error): OperationError.Builder? = OperationError.newBuilder()
        .setType(ErrorType.SERVICE_ERROR)
        .setMessage(if (it.message.isNullOrEmpty()) "" else it.message)

    private fun toCardType(type: ru.sbertroika.abt.emission.v1.CardType): CardType = CardType.newBuilder()
        .setId(type.id)
        .setName(type.name)
        .setRegion(type.region)
        .setDescription(type.description)
        .setImg(type.img)
        .setMask(type.mask)
        .setPreview(type.preview)
        .build()

    private fun toAbonement(abonement: ru.sbertroika.sdbp.connector.v1.Abonement): Abonement? {
        return Abonement.newBuilder()
            .setId(abonement.id)
            .setName(abonement.name)
            .setTemplateId(abonement.templateId)
            .setType(AbonementType.values()[abonement.typeValue])
            .setDescription(abonement.description)
            .setBalance(abonement.balance)
            .setRegion(abonement.region)
            .setStartDate(abonement.startDate)
            .setEndDate(abonement.endDate)
            .setIsActive(abonement.isActive)
            .setIsBlocked(abonement.isBlocked)
            .build()
    }
}