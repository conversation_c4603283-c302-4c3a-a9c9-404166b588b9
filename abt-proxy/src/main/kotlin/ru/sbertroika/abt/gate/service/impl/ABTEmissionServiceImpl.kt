package ru.sbertroika.abt.gate.service.impl

import arrow.core.Either
import com.google.protobuf.Empty
import io.grpc.ManagedChannelBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.abt.emission.v1.*
import ru.sbertroika.abt.gate.service.ABTEmissionService

@Service
class ABTEmissionServiceImpl(
    @Value("\${abt_emission_url}")
    private val abtEmissionServiceUrl: String
) : ABTEmissionService {

    private val channel = ManagedChannelBuilder.forTarget(abtEmissionServiceUrl)
        .usePlaintext()
        .enableRetry()
        .maxRetryAttempts(3)
        .build()
    private val client = ABTEmissionServiceGrpcKt.ABTEmissionServiceCoroutineStub(channel)
    
    override suspend fun emission(number: String?, uid: String?, type: String, region: String): Either<Error, String> {
        try {
            val request = EmissionRequest.newBuilder()
                .setNumber(number)
                .setUid(uid)
                .setType(type)
                .setRegion(region)
                .build()
            val response = client.emission(request)
            if (response.hasError()) return Either.Left(Error())
            return Either.Right(response.cardId)
        } catch (e: Exception) {
            return Either.Left(Error(e))
        }
    }

    override suspend fun getCardInfo(id: String): Either<Error, CardInfo> {
        try {
            val request = CardInfoRequest.newBuilder()
                .setCardId(id)
                .build()
            val response = client.cardInfo(request)
            if (response.hasError()) return Either.Left(Error())
            return Either.Right(response.info)
        } catch (e: Exception) {
            return Either.Left(Error(e))
        }
    }

    override suspend fun getCardTypes(): Either<Error, List<CardType>> {
        try {
            val response = client.cardTypes(Empty.getDefaultInstance())
            if (response.hasError()) return Either.Left(Error())
            return Either.Right(response.types.listList)
        } catch (e: Exception) {
            return Either.Left(Error(e))
        }
    }

    override suspend fun makeCard(number: String, type: String): Either<Error, MakeCardResult> {
        try {
            val request = MakeCardRequest.newBuilder()
                .setNumber(number)
                .setCardTypeId(type)
                .build()
            val client = ABTEmissionServiceGrpcKt.ABTEmissionServiceCoroutineStub(channel)
            val response = client.makeCard(request)
            if (response.hasError()) {
                return Either.Left(Error(response.error.message))
            }
            return Either.Right(response.result)
        } catch (e: Exception) {
            return Either.Left(Error(e))
        }
    }
}