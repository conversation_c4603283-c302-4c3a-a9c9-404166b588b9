package ru.sbertroika.abt.gate.service.impl

import arrow.core.Either
import arrow.core.computations.ResultEffect.bind
import arrow.core.flatMap
import io.grpc.ManagedChannelBuilder
import jakarta.annotation.PostConstruct
import kotlinx.coroutines.*
import org.apache.commons.logging.LogFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.abt.gate.model.WalletInfo
import ru.sbertroika.abt.gate.service.SDBPService
import ru.sbertroika.abt.gate.v1.SocialCategories
import ru.sbertroika.abt.gate.v1.SocialCategory
import ru.sbertroika.common.NotFound
import ru.sbertroika.common.ServiceError
import ru.sbertroika.common.v1.AbonementType
import ru.sbertroika.common.v1.ErrorType
import ru.sbertroika.sdbp.connector.v1.*
import java.util.concurrent.ConcurrentLinkedQueue

@Service
class SDBPServiceImpl(
    private val redisService: RedisServiceImpl,

    @Value("\${sdbp_gate_url}")
    private val sdbpGateUrl: String
) : SDBPService {

    private val log = LogFactory.getLog(this::class.java)

    @Value("\${regions}")
    lateinit var _regions: String

    @Value("\${request_to_region_timeout}")
    private var requestToRegionTimeout: Long = 5000

    private val regions: List<String> = mutableListOf()

    private val channel = ManagedChannelBuilder.forTarget(sdbpGateUrl)
        .usePlaintext()
        .enableRetry()
        .maxRetryAttempts(3)
        .build()
    private val client = SDBPServiceGrpcKt.SDBPServiceCoroutineStub(channel)

    @PostConstruct
    private fun setup() {
        _regions.replace("\"", "").split(";").forEach {
            (regions as ArrayList).add(it)
        }
    }

    override suspend fun abonementInfo(id: String, templateId: String, region: String): Either<Error, AbonementInfo> {
        try {
            val request = AbonementInfoRequest.newBuilder()
                .setId(id)
                .setRegion(region)
                .setTemplateId(templateId)
                .build()
            val response = client.abonementInfo(request)
            if (response.hasError()) return Either.Left(Error(response.error.message))
            return Either.Right(response.info)
        } catch (e: Exception) {
            log.error("Error request abonementInfo: id=$id, templateId=$templateId, region=$region")
            return Either.Left(Error(e))
        }
    }

    override suspend fun abonementList(cardUid: String, cardNum: String): Either<Error, List<Abonement>> {
        redisService.getCachedAbonementList(cardNum)
            .fold(
                { return Either.Left(Error(it.message)) },
                { abonements ->
                    if (abonements != null)
                        return Either.Right(abonements.map(this::toAbonementProto))
                })
        return requestRegions(cardNum, cardUid).flatMap { abonementsProto ->
            redisService.cacheAbonementList(cardNum, abonementsProto.map(this::toAbonementModel))
            Either.Right(abonementsProto)
        }
    }

    override suspend fun abonementByInvoice(invoiceId: String, region: String): Either<Throwable, AbonementInfo> =
        Either.catch {
            val response = client.abonementByInvoice(
                AbonementByInvoiceRequest.newBuilder()
                    .setInvoiceId(invoiceId)
                    .setRegion(region)
                    .build()
                )
            if (response.hasError()) {
                log.error("Error abonementByInvoice(): ${response.error.message}")
                return Either.Left(Error(response.error.message))
            }
            response.info
        }

    override suspend fun getCardInfoByNum(cardNum: String, region: String): Either<Error, CardInfo> {
        try {
            val request = CardInfoRequest.newBuilder()
                .setRegion(region)
                .setNumber(cardNum)
                .build()
            val response = client.cardInfo(request)
            if (response.hasError()) {
                if (response.error.type == ErrorType.NOT_FOUND) return Either.Left(NotFound(null))
                log.error("Error request cardInfo: region=$region, cardNum: $cardNum, errorCode: ${response.error.typeValue}, errorMessage: ${response.error.message}")
                return Either.Left(ServiceError(response.error.message))
            }
            return Either.Right(response.result)
        } catch (e: Exception) {
            log.error("Error request cardInfo: region=$region,, cardNum: $cardNum", e)
            return Either.Left(ServiceError(e.message))
        }
    }

    override suspend fun getSocialCategories(
        region: String,
        pan: String,
        ips: String?
    ): Either<Throwable, SocialCategories> = Either.catch{
        log.info("getSocialCategories() external call, region = $region, pan = $pan, ips = $ips")
        val request =
            SocialCategoriesRequest.newBuilder()
                .setRegion(region)
                .setPan(pan)
        ips?.let { request.setIps(it) }
        val response = client.socialCategories(request.build())
        if (response.hasError()) {
            log.info("error: getSocialCategories() external call, region = $region, pan = $pan, ips = $ips")
            return Either.Left(Error(response.error.message))
        }

        val list = response.result.socialCategoryList.map { mapCategory(it) }
        SocialCategories.newBuilder().addAllSocialCategory(list).build()
    }


    override suspend fun walletInfo(walletId: String, region: String): Either<Throwable, WalletInfo> = Either.catch {
        log.info("walletInfo walletId $walletId, region $region")
        val response = client.walletInfo(WalletInfoRequest.newBuilder().setId(walletId).setRegion(region).build())
        if(response.hasError()) {
            log.error("walletInfo error ${response.error.message}")
            throw Throwable(response.error.message)
        }
        val info = response.info
        log.info("walletInfo ok")
        WalletInfo(
            info.walletId,
            info.cardId,
            info.name,
            info.description,
            info.agentId,
            info.pan,
            info.panHash,
            info.uid,
            info.uidHash,
            info.isActive,
            info.balance,
            info.minReplenishment,
            info.maxReplenishment,
            info.number
        )
    }

    private fun mapCategory(category: ru.sbertroika.sdbp.connector.v1.SocialCategory): SocialCategory {
        val builder = SocialCategory.newBuilder()
            .setId(category.id)
            .setSocialType(category.socialType)
            .setName(category.name)
            .setShortName(category.shortName)
            .setCreatedAt(category.createdAt)
            .setCode(category.code)
            .setPriority(category.priority)
        category.updatedAt?.let { builder.setUpdatedAt(it) }
        return builder.build()
    }

    private suspend fun requestRegions(cardNum: String, cardUid: String): Either<Error, List<Abonement>> = coroutineScope {
        val res = ConcurrentLinkedQueue<Abonement>()
        regions.map { region ->
            async(Dispatchers.IO) {
                withTimeoutOrNull(requestToRegionTimeout) {
                    kotlin.runCatching {
                        val regionAbonements = reqAbonementList(region, cardUid, cardNum)
                        res.addAll(regionAbonements.bind())
                    }
                }
            }
        }.awaitAll()
        Either.Right(res.toList())
    }

    private suspend fun reqAbonementList(region: String, cardUid: String, cardNum: String): Either<Error, List<Abonement>> {
        try {
            val request = AbonementListRequest.newBuilder()
                .setCardUid(cardUid)
                .setCardNum(cardNum)
                .setRegion(region)
                .build()
            val response = client.abonementList(request)
            if (response.hasError()) {
                log.error("Error request abonement list: region=$region. cardUid: $cardUid, cardNum: $cardNum, errorCode: ${response.error.typeValue}, errorMessage: ${response.error.message}")
                return Either.Right(emptyList())
            }
            return Either.Right(response.abonements.abonementList)
        } catch (e: Exception) {
            log.error("Error request abonement list: region=$region. cardUid: $cardUid, cardNum: $cardNum", e)
            return Either.Right(emptyList())
        }
    }

    private fun toAbonementModel(abonement: Abonement): ru.sbertroika.abt.gate.model.Abonement {
        return ru.sbertroika.abt.gate.model.Abonement(
            id = abonement.id,
            name = abonement.name,
            templateId = abonement.templateId,
            typeValue = abonement.typeValue,
            description = abonement.description,
            balance = abonement.balance,
            region = abonement.region,
            startDate = abonement.startDate,
            endDate = abonement.endDate,
            isActive = abonement.isActive,
            isBlocked = abonement.isBlocked
        )
    }

    private fun toAbonementProto(abonement: ru.sbertroika.abt.gate.model.Abonement): Abonement {
        return Abonement.newBuilder()
            .setId(abonement.id)
            .setName(abonement.name)
            .setTemplateId(abonement.templateId)
            .setType(AbonementType.values()[abonement.typeValue])
            .setDescription(abonement.description)
            .setBalance(abonement.balance)
            .setRegion(abonement.region)
            .setStartDate(abonement.startDate)
            .setEndDate(abonement.endDate)
            .setIsActive(abonement.isActive)
            .setIsBlocked(abonement.isBlocked)
            .build()
    }
}