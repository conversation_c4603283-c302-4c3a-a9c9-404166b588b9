package ru.sbertroika.abt.gate.service

import arrow.core.Either
import ru.sbertroika.abt.gate.model.WalletInfo
import ru.sbertroika.abt.gate.v1.SocialCategories
import ru.sbertroika.sdbp.connector.v1.Abonement
import ru.sbertroika.sdbp.connector.v1.AbonementInfo
import ru.sbertroika.sdbp.connector.v1.CardInfo

interface SDBPService {

    suspend fun abonementInfo(id: String, templateId: String, region: String): Either<Error, AbonementInfo>

    suspend fun abonementList(cardUid: String, cardNum: String): Either<Error, List<Abonement>>

    suspend fun abonementByInvoice(invoiceId: String, region: String): Either<Throwable, AbonementInfo>

    suspend fun getCardInfoByNum(cardNum: String, region: String): Either<Error, CardInfo>

    suspend fun getSocialCategories(region: String, pan: String, ips: String?): Either<Throwable, SocialCategories>

    suspend fun walletInfo(walletId: String, region: String): Either<Throwable, WalletInfo>
}