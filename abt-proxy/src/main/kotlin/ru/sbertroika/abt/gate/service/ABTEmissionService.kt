package ru.sbertroika.abt.gate.service

import arrow.core.Either
import ru.sbertroika.abt.emission.v1.CardInfo
import ru.sbertroika.abt.emission.v1.CardType
import ru.sbertroika.abt.emission.v1.MakeCardResult

interface ABTEmissionService {

    suspend fun emission(number: String?, uid: String?, type: String, region: String): Either<Error, String>

    suspend fun getCardInfo(id: String): Either<Error, CardInfo>

    suspend fun getCardTypes(): Either<Error, List<CardType>>

    suspend fun makeCard(number: String, type: String): Either<Error, MakeCardResult>
}