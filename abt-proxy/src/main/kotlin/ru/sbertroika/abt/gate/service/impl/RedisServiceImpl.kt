package ru.sbertroika.abt.gate.service.impl

import arrow.core.Either
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Service
import ru.sbertroika.abt.gate.model.Abonement
import java.time.Duration

@Service
class RedisServiceImpl(
    private val redisTemplateAbonement: RedisTemplate<String, Abonement>,

    @Value("\${region_abonements_cache_timeout}")
    private val regionAbonementsCacheTimeout: Long
) {

    companion object {
        const val ABONEMENT_LIST_KEY = "abt-gate:abonement-list"
    }

    private fun abonementsKey(cardNum: String) = ABONEMENT_LIST_KEY + cardNum

    suspend fun cacheAbonementList(cardNum: String, value: List<Abonement>):
            Either<Error, List<Abonement>> = try {
        if (value.isNotEmpty()) {
            val key = abonementsKey(cardNum)
            redisTemplateAbonement.delete(key)
            redisTemplateAbonement.opsForList().rightPushAll(key, value)
            redisTemplateAbonement.expire(key, Duration.ofMinutes(regionAbonementsCacheTimeout))
            Either.Right(value)
        } else {
            Either.Right(emptyList())
        }
    } catch (e: Exception) {
        Either.Left(Error(e.message))
    }

    fun getCachedAbonementList(cardNum: String) : Either<Error, List<Abonement>?> = try {
        val key = abonementsKey(cardNum)
        Either.Right(
            if (redisTemplateAbonement.hasKey(key)) {
                redisTemplateAbonement.opsForList().range(key, 0, -1)
            } else null
        )
    } catch (e: Exception) {
        Either.Left(Error(e))
    }
}