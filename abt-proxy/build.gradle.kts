import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    idea
    kotlin("jvm")
    kotlin("plugin.spring") version libs.versions.kotlin.get() apply false
    id("com.google.protobuf") version libs.versions.protobufPlugin.get()
    id("org.springframework.boot") version "3.5.4"
    id("io.spring.dependency-management") version "1.1.0"
}

group = "ru.sbertroika.passenger"
version = rootProject.version

java.sourceCompatibility = JavaVersion.VERSION_17

configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
}

repositories {
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
}

dependencies {
    implementation(project(":abt-proxy-api"))

    implementation("ru.sbertroika.common:common-api:1.0.6")
    implementation("ru.sbertroika.abt:abt-emission-api:1.0.17")
    implementation("ru.sbertroika.tkp2:sdbp-connector-api:1.1.0")

    implementation("io.github.lognet:grpc-spring-boot-starter:5.2.0") {
        exclude(group = "io.grpc", module = "grpc-netty-shaded")
    }

    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("org.springframework.boot:spring-boot-starter-data-redis")
    implementation("org.springframework.kafka:spring-kafka:3.3.8")

    //Logging
    implementation("org.springframework.boot:spring-boot-starter-logging")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.8.1")
    implementation("org.zalando:logbook-spring-boot-webflux-autoconfigure:3.10.0")
    implementation("org.zalando:logbook-okhttp:3.10.0")

    //Kotlin
    implementation("io.projectreactor.kotlin:reactor-kotlin-extensions")
    implementation(libs.kotlin.reflect)

    implementation(libs.kotlin.stdlib)

    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.15.0")

    //Kotlin-ext
    implementation("io.arrow-kt:arrow-core:1.0.1")

    implementation("redis.clients:jedis")

    //Lombok
    compileOnly("org.projectlombok:lombok")
    annotationProcessor("org.projectlombok:lombok")

    //gRPC
    implementation("io.grpc:grpc-kotlin-stub:1.4.3")
    implementation("io.grpc:grpc-protobuf:1.71.0")
    implementation("io.grpc:grpc-stub:1.71.0")
    implementation("io.grpc:grpc-protobuf-lite:1.71.0")
    implementation("io.grpc:grpc-netty:1.71.0")
    implementation("com.google.protobuf:protobuf-java:3.21.7")
    implementation("com.google.protobuf:protobuf-kotlin:3.21.7")

    //Test
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    testCompileOnly("org.projectlombok:lombok")
    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        // Exclude the test engine you don't need
        exclude(group = "org.junit.vintage", module = "junit-vintage-engine")
    }
    testImplementation("com.squareup.okhttp3:okhttp:4.10.0")
    testImplementation("com.squareup.okhttp3:logging-interceptor:4.10.0")
}

kotlin {
    jvmToolchain(17)
}

tasks.withType<KotlinCompile>().configureEach {
    compilerOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
    }
}

tasks.withType<Test>().configureEach {
    val skipProvider = providers.gradleProperty("skipTest")
    if (!skipProvider.isPresent()) {
        useJUnitPlatform()
        testLogging {
            showStandardStreams = true
        }
    }
}

tasks.named<Jar>("jar") {
    enabled = false
}
