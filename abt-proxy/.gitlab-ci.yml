include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/java-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates

# ========================
# abt-proxy: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
abt_proxy_build_develop:
  stage: build
  needs: [develop_build_test_publish]
  variables:
    SERVICE_NAME: "abt-proxy"
    TAG: "$CI_COMMIT_SHORT_SHA"
  extends:
    - .docker_gradle_build_and_push
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - abt-proxy/**
        - ./**


abt_proxy_helm_kubeval_testing_develop:
  stage: test
  needs:
    - job: abt_proxy_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    SERVICE_NAME: "abt-proxy"
  extends:
    - .validate_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - abt-proxy/**
        - charts/abt-proxy/**


abt_proxy_deploy_chart_develop:
  stage: deploy
  needs:
    - abt_proxy_helm_kubeval_testing_develop
    - job: abt_proxy_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    STAGE: "dev"
    SERVICE_NAME: "abt-proxy"
  extends:
    - .deploy_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - abt-proxy/**
        - charts/abt-proxy/**

# --- TAG ---
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - abt-proxy/**

abt_proxy_build_tag:
  stage: build
  variables:
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "abt-proxy"
  extends:
    - .docker_gradle_build_and_push
  <<: *tag_rules

abt_proxy_helm_kubeval_testing_tag:
  stage: test
  needs:
    - abt_proxy_build_tag
  variables:
    STAGE: "dev"
    SERVICE_NAME: "abt-proxy"
  extends:
    - .validate_helm_template
  <<: *tag_rules

abt_proxy_deploy_chart_tag:
  stage: deploy
  needs:
    - abt_proxy_helm_kubeval_testing_tag
  variables:
    STAGE: "dev"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "abt-proxy"
  extends:
    - .deploy_helm_template
  <<: *tag_rules

abt_proxy_deploy_prod:
  stage: deploy
  needs:
    - abt_proxy_deploy_chart_tag
  variables:
    STAGE: "prod"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "abt-proxy"
    NAMESPACE: "$KUBE_NAMESPACE-prod"
    KUBECONFIG_FILE: $KUBECONFIG_CCE_PROD
  extends:
    - .deploy_helm_template
  when: manual
  <<: *tag_rules

