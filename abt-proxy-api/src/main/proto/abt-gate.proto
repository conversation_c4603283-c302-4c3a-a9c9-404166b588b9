syntax = "proto3";

package ru.sbertroika.abt.gate.v1;

import "common.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.abt.gate.v1";

service ABTGateService {
  rpc emission(EmissionRequest) returns (EmissionResponse);
  rpc abonementInfo(AbonementInfoRequest) returns (AbonementInfoResponse);
  rpc abonementList(AbonementListRequest) returns (AbonementListResponse);
  rpc abonementByInvoice(AbonementByInvoiceRequest) returns (AbonementInfoResponse);
  rpc cardTypes(google.protobuf.Empty) returns (CardTypesResponse);
  rpc cardInfo(CardInfoRequest) returns (CardInfoRequestResponse);
  rpc socialCategories(SocialCategoriesRequest) returns (SocialCategoriesResponse);
  rpc walletInfo(WalletInfoRequest) returns (WalletInfoResponse);
}

message CardType {
  string id = 1;
  string region = 2;
  string name = 3;                    // Наименование средства предъявления
  string description = 4;             // Детальная информация о средстве предъявления
  string mask = 5;                    // Маска ввода печатного номера
  string prefix = 6;                  // Префикс для полного номера карты
  string img = 10;                    // Ссылка на изображение средства предъявления
  string preview = 11;                // Ссылка на иконку средства предъявления
  map<string, string> patterns = 12;  // Условие проверки валидности печатного номера
}

message EmissionRequest {
  string number = 1;
  string uid = 2;
  string region = 3;
  string type = 4;
}

message EmissionResponse {
  oneof response {
    ru.sbertroika.common.v1.OperationError error  = 1;
    string cardId = 2;
  }
}

message AbonementInfo {
  string id = 1;
  string name = 2;
  ru.sbertroika.common.v1.AbonementType type = 3;
  string carrierId = 4;
}

message AbonementInfoRequest {
  string id = 1;
  string templateId = 2;
  string region = 3;
}

message AbonementInfoResponse {
  oneof response {
    ru.sbertroika.common.v1.OperationError error = 1;
    AbonementInfo info = 2;
  }
}

message AbonementListRequest {
  string carrierId = 1;
}

message Abonement {
  string id = 1;
  string templateId = 2;
  ru.sbertroika.common.v1.AbonementType type = 3;
  string region = 4;
  string name = 5;
  string description = 6;
  int64 startDate = 7;
  int64 endDate = 8;
  int32 balance = 9;
  bool isActive = 10;
  bool isBlocked = 11;
}

message Abonements {
  repeated Abonement abonement = 1;
}

message AbonementListResponse {
  oneof response {
    ru.sbertroika.common.v1.OperationError error = 1;
    Abonements abonements = 2;
  }
}

message AbonementByInvoiceRequest {
  string invoiceId = 1;
  string region = 2;
}

message CardTypes {
  repeated CardType list = 1;
}

message CardTypesResponse {
  oneof response {
    common.v1.OperationError error = 1;
    CardTypes types = 2;
  }
}

message CardInfoRequest {
  string cardId = 1;
}

message CardInfo {
  string id = 1;
  string number = 2;
  string uid = 3;
  string region = 4;
  CardType type = 5;
}

message CardInfoRequestResponse {
  oneof response {
    ru.sbertroika.common.v1.OperationError error  = 1;
    CardInfo info = 2;
  }
}


message SocialCategoriesResponse {
  oneof response {
    ru.sbertroika.common.v1.OperationError error = 1;
    SocialCategories result = 2;
  }
}

message SocialCategories {
  repeated SocialCategory socialCategory = 1;
}

message SocialCategory {
  int64 id = 1;
  int32 socialType = 2;
  string name = 3;
  string shortName = 4;
  optional int32 priority = 5;
  optional int32 code = 6;
  google.protobuf.Timestamp createdAt = 7;
  optional google.protobuf.Timestamp updatedAt = 8;
}

message SocialCategoriesRequest {
  string region = 1;                  // Для БК сейчас по всем регионам (можно не указывать), Для ТК по указанному региону
  string pan = 2;                     // Для ТК - номер карты, для БК - PAN
  optional string ips = 3;            // Если указан, то БК (значения: VISA, MASTERCARD, MIR), иначе ТК
}

message WalletInfoRequest {
  string id = 1;
  string region = 2;
}

message WalletInfoResponse {
  oneof response {
    ru.sbertroika.common.v1.OperationError error = 1;
    WalletInfo info = 2;
  }
}

message WalletInfo {
  uint64 walletId = 1;
  optional uint32 cardId = 2;
  string name = 3;
  optional string description = 4;
  optional uint64 agentId = 5;
  optional string pan = 6;
  optional string panHash = 7;
  optional string uid = 8;
  optional string uidHash = 9;
  bool isActive = 10;
  uint64 balance = 11;
  uint32 minReplenishment = 12;
  uint32 maxReplenishment = 13;
  optional string number = 14;
}